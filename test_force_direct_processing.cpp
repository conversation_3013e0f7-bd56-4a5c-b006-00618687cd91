#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include "legacy/network/ultrafasttls.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    std::cout << "=== UltraFastTLS 最优先级处理模式测试 ===" << std::endl;
    
    // 创建UltraFastTLS实例
    UltraFastTLS tls;
    
    // 测试默认配置
    std::cout << "默认配置 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    // 测试设置最优先级处理模式
    tls.setForceDirectProcessing(true);
    std::cout << "设置后 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    // 测试关闭最优先级处理模式
    tls.setForceDirectProcessing(false);
    std::cout << "关闭后 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    // 重新开启用于实际测试
    tls.setForceDirectProcessing(true);
    std::cout << "重新开启 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    std::cout << "\n=== 最优先级处理模式说明 ===" << std::endl;
    std::cout << "开启时的处理逻辑：" << std::endl;
    std::cout << "1. 🚀 接收服务器分批发送的所有包，按Content-Length拼接完整" << std::endl;
    std::cout << "2. 🎯 不判断gzip数据完整性，直接处理整个HTTP响应体" << std::endl;
    std::cout << "3. 📦 服务器的包虽然分批但基本同时到达，一次性全部接收" << std::endl;
    std::cout << "4. 🗜️ 解压时不验证服务器声明的大小，直接解压整个数据" << std::endl;
    std::cout << "5. ⚡ 只要有解压数据就直接返回，不要求严格的Z_STREAM_END" << std::endl;
    std::cout << "\n核心优势：" << std::endl;
    std::cout << "- 跳过所有耗时的完整性检查" << std::endl;
    std::cout << "- 直接处理分批包拼接后的完整HTTP数据" << std::endl;
    std::cout << "- 最大化网络处理性能" << std::endl;
    
    std::cout << "\n测试完成！" << std::endl;
    
    return 0;
}
