#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include "legacy/network/ultrafasttls.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    std::cout << "=== UltraFastTLS 最优先级处理模式测试 ===" << std::endl;
    
    // 创建UltraFastTLS实例
    UltraFastTLS tls;
    
    // 测试默认配置
    std::cout << "默认配置 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    // 测试设置最优先级处理模式
    tls.setForceDirectProcessing(true);
    std::cout << "设置后 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    // 测试关闭最优先级处理模式
    tls.setForceDirectProcessing(false);
    std::cout << "关闭后 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    // 重新开启用于实际测试
    tls.setForceDirectProcessing(true);
    std::cout << "重新开启 - 最优先级处理模式: " << (tls.isForceDirectProcessing() ? "开启" : "关闭") << std::endl;
    
    std::cout << "\n=== 配置说明 ===" << std::endl;
    std::cout << "最优先级处理模式开启时：" << std::endl;
    std::cout << "1. 接收到数据包后直接按Content-Length判断完整性" << std::endl;
    std::cout << "2. 不进行gzip数据完整性检查" << std::endl;
    std::cout << "3. 解压时不验证服务器声明的大小" << std::endl;
    std::cout << "4. 只要有解压数据就直接返回，不要求严格的Z_STREAM_END" << std::endl;
    
    std::cout << "\n测试完成！" << std::endl;
    
    return 0;
}
