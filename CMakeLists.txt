cmake_minimum_required(VERSION 3.21)

project(OrderManager LANGUAGES CXX)

# ------------------------------------------------------------
# vcpkg 工具链配置 (超高性能依赖管理)
# ------------------------------------------------------------
# 设置用户指定的vcpkg路径
set(VCPKG_ROOT "C:/Users/<USER>/Desktop/vcpkg-master/vcpkg-master")
set(CMAKE_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake" CACHE STRING "Vcpkg toolchain file")

# 设置vcpkg目标架构
set(VCPKG_TARGET_TRIPLET "x64-windows" CACHE STRING "Vcpkg target triplet")

# 优先使用vcpkg的静态库以获得最佳性能
set(VCPKG_LIBRARY_LINKAGE "static" CACHE STRING "Vcpkg library linkage")

message(STATUS "🔧 Using vcpkg from: ${VCPKG_ROOT}")
message(STATUS "🎯 Target triplet: ${VCPKG_TARGET_TRIPLET}")

# ------------------------------------------------------------
# Locate required Qt modules
# ------------------------------------------------------------
find_package(Qt6 REQUIRED COMPONENTS Widgets Network Concurrent)

# ------------------------------------------------------------
# 添加OpenSSL支持 (超高性能TLS伪装)
# ------------------------------------------------------------
# 多种安装方式支持，优先级：官网OpenSSL > 用户指定vcpkg > 系统安装 > 手动指定
set(OPENSSL_SEARCH_PATHS
    "C:/Program Files/OpenSSL-Win64"          # Win64OpenSSL-3_5_1.exe安装路径 (优先)
    "C:/Users/<USER>/Desktop/vcpkg-master/vcpkg-master/installed/x64-windows"  # 用户指定的vcpkg路径
    "C:/OpenSSL-Win64"                        # 备用路径
    "C:/vcpkg/installed/x64-windows"          # 默认vcpkg安装路径
    "C:/Libraries/OpenSSL"                    # 自定义路径
)

# 尝试查找OpenSSL (优先使用vcpkg版本以获得最佳性能)
find_package(OpenSSL QUIET)

if(NOT OpenSSL_FOUND)
    message(STATUS "🔍 OpenSSL not found via find_package, trying manual search...")

    foreach(SEARCH_PATH ${OPENSSL_SEARCH_PATHS})
        if(EXISTS "${SEARCH_PATH}/include/openssl/ssl.h")
            set(OPENSSL_ROOT_DIR "${SEARCH_PATH}")
            set(OPENSSL_INCLUDE_DIR "${SEARCH_PATH}/include")

            # 查找库文件 (支持多种命名约定和路径)
            find_library(OPENSSL_SSL_LIBRARY
                NAMES libssl ssl ssleay32 ssl-3-x64 libssl-3-x64
                PATHS
                    "${SEARCH_PATH}/lib/VC/x64/MD"
                    "${SEARCH_PATH}/lib/VC/x64/MT"
                    "${SEARCH_PATH}/lib/VC/x64"
                    "${SEARCH_PATH}/lib/x64"
                    "${SEARCH_PATH}/lib"
                NO_DEFAULT_PATH)
            find_library(OPENSSL_CRYPTO_LIBRARY
                NAMES libcrypto crypto libeay32 crypto-3-x64 libcrypto-3-x64
                PATHS
                    "${SEARCH_PATH}/lib/VC/x64/MD"
                    "${SEARCH_PATH}/lib/VC/x64/MT"
                    "${SEARCH_PATH}/lib/VC/x64"
                    "${SEARCH_PATH}/lib/x64"
                    "${SEARCH_PATH}/lib"
                NO_DEFAULT_PATH)

            if(OPENSSL_SSL_LIBRARY AND OPENSSL_CRYPTO_LIBRARY)
                set(OPENSSL_LIBRARIES ${OPENSSL_SSL_LIBRARY} ${OPENSSL_CRYPTO_LIBRARY})
                set(OpenSSL_FOUND TRUE)

                # 检测OpenSSL版本以优化性能配置
                if(EXISTS "${SEARCH_PATH}/include/openssl/opensslv.h")
                    file(READ "${SEARCH_PATH}/include/openssl/opensslv.h" OPENSSL_VERSION_FILE)
                    string(REGEX MATCH "OPENSSL_VERSION_TEXT[ \t]+\"OpenSSL ([0-9]+\\.[0-9]+\\.[0-9]+)" _ ${OPENSSL_VERSION_FILE})
                    set(OPENSSL_VERSION ${CMAKE_MATCH_1})
                endif()

                message(STATUS "✅ OpenSSL ${OPENSSL_VERSION} found at: ${SEARCH_PATH}")
                message(STATUS "📚 SSL Library: ${OPENSSL_SSL_LIBRARY}")
                message(STATUS "📚 Crypto Library: ${OPENSSL_CRYPTO_LIBRARY}")
                break()
            endif()
        endif()
    endforeach()
endif()

if(NOT OpenSSL_FOUND)
    message(WARNING "⚠️ OpenSSL not found! 将使用Windows原生TLS (SChannel)")
    message(STATUS "📝 建议安装步骤:")
    message(STATUS "  🥇 方法1: 运行 Win64OpenSSL-3_5_1.exe")
    message(STATUS "  🔧 方法2: cd ${VCPKG_ROOT} && vcpkg install openssl:x64-windows")
    message(STATUS "  📦 方法3: 官网下载: https://slproweb.com/products/Win32OpenSSL.html")

    # 使用Windows原生TLS作为备选方案
    set(USE_WINDOWS_TLS TRUE)
    add_definitions(-DUSE_WINDOWS_TLS)
else()
    set(USE_WINDOWS_TLS FALSE)

    # 启用OpenSSL性能优化宏
    if(OPENSSL_VERSION VERSION_GREATER_EQUAL "3.0.0")
        add_definitions(-DOPENSSL_FOUND -DOPENSSL_3_PLUS)
        message(STATUS "🚀 启用OpenSSL 3.x+ 性能优化")
    else()
        add_definitions(-DOPENSSL_FOUND)
    endif()
endif()

if(OpenSSL_FOUND)
    message(STATUS "🔐 OpenSSL found: ${OPENSSL_VERSION}")
    message(STATUS "📍 OpenSSL include: ${OPENSSL_INCLUDE_DIR}")
    message(STATUS "📍 OpenSSL libraries: ${OPENSSL_LIBRARIES}")
endif()

# ------------------------------------------------------------
# 🚀 超高性能编译优化 (极限性能模式)
# ------------------------------------------------------------

# 设置Release为默认构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Release" CACHE STRING "Build type" FORCE)
    message(STATUS "🎯 默认构建类型设置为: Release")
endif()

# 全局性能优化宏定义
add_definitions(
    -DNDEBUG                    # 禁用断言
    -D_SECURE_SCL=0            # 禁用STL安全检查
    -D_HAS_ITERATOR_DEBUGGING=0 # 禁用迭代器调试
    -DWIN32_LEAN_AND_MEAN      # 精简Windows头文件
    -DNOMINMAX                 # 禁用Windows min/max宏
    -D_CRT_SECURE_NO_WARNINGS  # 禁用CRT安全警告
)

if(CMAKE_BUILD_TYPE STREQUAL "Release")
    message(STATUS "🚀 启用极限性能优化模式")

    # MSVC编译器优化 (Visual Studio)
    if(MSVC)
        target_compile_options(OrderManager PRIVATE
            /O2                     # 最大速度优化
            /Ob2                    # 内联函数展开
            /Oi                     # 启用内置函数
            /Ot                     # 偏向速度优化
            /Oy                     # 省略帧指针
            /GL                     # 全程序优化
            /GS-                    # 禁用安全检查
            /Gy                     # 启用函数级链接
            /fp:fast                # 快速浮点模式
            /arch:AVX2              # 启用AVX2指令集
            /favor:INTEL64          # 优化Intel 64位
            /bigobj                 # 支持大对象文件
        )

        # MSVC链接器优化
        target_link_options(OrderManager PRIVATE
            /LTCG                   # 链接时代码生成
            /OPT:REF                # 移除未引用函数
            /OPT:ICF                # 合并相同函数
            /INCREMENTAL:NO         # 禁用增量链接
        )

        message(STATUS "✅ MSVC极限优化已启用")
    endif()

    # 通用优化选项（GCC和Clang共享）
    set(COMMON_OPTIMIZE_FLAGS
        -O3                     # 最高优化级别
        -flto                   # 链接时优化
        -march=native           # 针对当前CPU优化
        -ffast-math             # 快速数学运算
        -funroll-loops          # 循环展开
        -finline-functions      # 内联函数
        -fomit-frame-pointer    # 省略帧指针
    )

    # GCC编译器优化
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        target_compile_options(OrderManager PRIVATE
            ${COMMON_OPTIMIZE_FLAGS}
            -mtune=native           # 针对当前CPU调优（GCC特有）
            -fno-stack-protector    # 禁用栈保护
            -fno-exceptions         # 禁用异常处理
            -fno-rtti               # 禁用RTTI
        )

        target_link_options(OrderManager PRIVATE
            -flto                   # 链接时优化
            -Wl,--gc-sections       # 垃圾回收未使用段
            -s                      # 去除符号表
        )

        message(STATUS "✅ GCC极限优化已启用")
    endif()

    # Clang编译器优化
    if(CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
        target_compile_options(OrderManager PRIVATE ${COMMON_OPTIMIZE_FLAGS})
        message(STATUS "✅ Clang极限优化已启用")
    endif()

    # 全局链接时优化
    set_target_properties(OrderManager PROPERTIES
        INTERPROCEDURAL_OPTIMIZATION TRUE
        CXX_VISIBILITY_PRESET hidden
        VISIBILITY_INLINES_HIDDEN TRUE
    )

    message(STATUS "🎯 目标架构: ${CMAKE_SYSTEM_PROCESSOR}")
    message(STATUS "🔧 编译器: ${CMAKE_CXX_COMPILER_ID} ${CMAKE_CXX_COMPILER_VERSION}")

else()
    message(STATUS "🐛 调试模式 - 性能优化已禁用")
endif()

# ------------------------------------------------------------
# 添加zlib支持 (优先使用vcpkg版本)
# ------------------------------------------------------------
find_package(ZLIB QUIET)

if(NOT ZLIB_FOUND)
    # 尝试通过vcpkg查找
    if(EXISTS "${VCPKG_ROOT}/installed/x64-windows/include/zlib.h")
        set(ZLIB_INCLUDE_DIRS "${VCPKG_ROOT}/installed/x64-windows/include")
        find_library(ZLIB_LIBRARIES
            NAMES z zlib zlibstatic
            PATHS "${VCPKG_ROOT}/installed/x64-windows/lib"
            NO_DEFAULT_PATH)
        if(ZLIB_LIBRARIES)
            set(ZLIB_FOUND TRUE)
            message(STATUS "✅ 找到vcpkg zlib: ${ZLIB_LIBRARIES}")
        endif()
    endif()

    # 如果还是没找到，尝试本地路径
    if(NOT ZLIB_FOUND AND EXISTS "C:/Libraries/zlib131/zlib-1.3.1")
        add_subdirectory("C:/Libraries/zlib131/zlib-1.3.1" ${CMAKE_BINARY_DIR}/zlib)
        set(ZLIB_FOUND TRUE)
        set(ZLIB_TARGET "zlibstatic")
        message(STATUS "✅ 使用本地zlib源码")
    endif()
endif()

if(ZLIB_FOUND)
    message(STATUS "✅ zlib已找到")
    add_definitions(-DHAS_ZLIB)
else()
    message(WARNING "⚠️ 未找到zlib，将禁用压缩功能")
    add_definitions(-DNO_ZLIB)
endif()

# ------------------------------------------------------------
# Apply Qt-recommended project defaults (C++17, AUTOMOC/UIC/RCC …)
# ------------------------------------------------------------
qt_standard_project_setup()

# ------------------------------------------------------------
# Executable target
# ------------------------------------------------------------
qt_add_executable(OrderManager
    WIN32            # Use GUI subsystem on Windows (no console window)
    main.cpp
    # UI 文件 (新架构)
    src/ui/mainwindow.h
    src/ui/mainwindow.cpp
    src/ui/mainwindow.ui
    # 登录模块 (新架构)
    src/login/async_login_manager.h
    src/login/async_login_manager.cpp
    # API 文件 (遗留代码)
    legacy/api/orderapi.h
    legacy/api/orderapi.cpp
    legacy/api/api_constants.h
    # 工作线程 (遗留代码)
    legacy/workers/filterworker.h
    legacy/workers/filterworker.cpp
    # 网络模块 (遗留代码)
    legacy/network/ultrafasttls.h
    legacy/network/ultrafasttls.cpp
    legacy/network/request_builder.h
    legacy/network/request_builder.cpp
    legacy/network/response_processor.h
    legacy/network/response_processor.cpp
    # 工具类 (遗留代码)
    legacy/utils/simple_logger.h
    legacy/utils/simple_logger.cpp
    legacy/utils/utils.h
    legacy/utils/error_handler.h
    legacy/utils/error_handler.cpp
    legacy/utils/json_parser.h
    legacy/utils/json_parser.cpp

    # 服务模块 (遗留代码)
    legacy/services/encryption_service.h
    legacy/services/encryption_service.cpp
    legacy/services/authentication_service.h
    legacy/services/authentication_service.cpp
    legacy/services/service_container.h
    legacy/services/service_container.cpp
    legacy/services/business_logic.h
    legacy/services/business_logic.cpp

    # 其他工具类
    # functional_utils.h - 已删除，功能已整合到 legacy/utils/utils.h

    # 重构后的新架构组件 (渐进式启用)
    src/config/app_config.h
    src/config/app_config.cpp
    src/core/utils/logger.h
    src/core/utils/logger.cpp
    src/core/workers/account_worker.h
    src/core/workers/account_worker.cpp
    src/core/managers/simple_refresh_manager.h
    src/core/managers/simple_refresh_manager.cpp
    # src/core/models/account.h  # 暂时禁用，需要更多重构
    # src/core/models/account.cpp
    # src/core/models/order.h
    # src/core/models/order.cpp
    # src/core/services/order_service.h
    # src/core/services/order_service.cpp
    # src/core/controllers/app_controller.h
    # src/core/controllers/app_controller.cpp
    src/network/engines/network_engine_interface.h
    src/network/engines/ultrafasttls_adapter.h
    src/network/engines/ultrafasttls_adapter.cpp
    # src/network/network_manager.h
    # src/network/network_manager.cpp
    src/core/utils/json_helper.h
    src/core/utils/string_utils.h
    # src/integration/modern_integration.h
    # src/integration/modern_integration.cpp

    # 渐进式重组适配器 (第一阶段)
    src/integration/legacy_api_adapter.h
    src/integration/legacy_api_adapter.cpp
)

# ------------------------------------------------------------
# Compile features & warnings
# ------------------------------------------------------------
# Ensure C++17 (qt_standard_project_setup already sets this, but we make it explicit for clarity)
target_compile_features(OrderManager PRIVATE cxx_std_17)

# Consistent warning levels across compilers
# MSVC  : /W4  + /permissive-  (standard-conformance)
# GCC/Clang: -Wall -Wextra -Wpedantic
# Users can override these from the command line if desired.
target_compile_options(OrderManager PRIVATE
    $<$<CXX_COMPILER_ID:MSVC>:/W4 /permissive->
    $<$<NOT:$<CXX_COMPILER_ID:MSVC>>:-Wall -Wextra -Wpedantic>
)

# ------------------------------------------------------------
# Link Qt libraries (and any future third-party libs)
# ------------------------------------------------------------
# 根据找到的库来设置链接
set(LINK_LIBRARIES
    Qt6::Widgets
    Qt6::Network
    Qt6::Concurrent
    ws2_32      # Windows Socket API
    crypt32     # Windows Crypto API
)

# 添加zlib库
if(ZLIB_FOUND)
    if(DEFINED ZLIB_TARGET)
        list(APPEND LINK_LIBRARIES ${ZLIB_TARGET})
    else()
        list(APPEND LINK_LIBRARIES ${ZLIB_LIBRARIES})
        target_include_directories(OrderManager PRIVATE ${ZLIB_INCLUDE_DIRS})
    endif()
endif()

# 添加OpenSSL库
if(OpenSSL_FOUND)
    list(APPEND LINK_LIBRARIES ${OPENSSL_LIBRARIES})
    target_include_directories(OrderManager PRIVATE ${OPENSSL_INCLUDE_DIR})
    message(STATUS "✅ 将链接OpenSSL库: ${OPENSSL_LIBRARIES}")
else()
    # 添加Windows原生TLS库
    list(APPEND LINK_LIBRARIES secur32 schannel)
    message(STATUS "⚠️ 使用Windows原生TLS，未链接OpenSSL")
endif()

# 执行链接
target_link_libraries(OrderManager PRIVATE ${LINK_LIBRARIES})

message(STATUS "🔗 最终链接库列表: ${LINK_LIBRARIES}")

# ------------------------------------------------------------
# Output directory (optional)
# ------------------------------------------------------------
# Uncomment to place the executable inside the build tree's bin/ sub-folder
# set_target_properties(OrderManager PROPERTIES RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin") 

# ------------------------------------------------------------
# 统一包含目录配置 (代码整理：解决相对路径混乱)
# ------------------------------------------------------------
# 设置项目根目录为包含基础路径，统一所有#include语句
target_include_directories(${PROJECT_NAME} PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}                    # 项目根目录
    ${CMAKE_CURRENT_SOURCE_DIR}/src                # 新架构源码
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core           # 核心组件
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core/utils     # 核心工具
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core/models    # 数据模型
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core/services  # 业务服务
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core/controllers # 控制器
    ${CMAKE_CURRENT_SOURCE_DIR}/src/network        # 网络层
    ${CMAKE_CURRENT_SOURCE_DIR}/src/network/engines # 网络引擎
    ${CMAKE_CURRENT_SOURCE_DIR}/src/integration    # 集成适配
    ${CMAKE_CURRENT_SOURCE_DIR}/src/config         # 配置管理
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ui             # 用户界面
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy             # 遗留代码
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy/api         # 遗留API
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy/network     # 遗留网络
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy/services    # 遗留服务
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy/utils       # 遗留工具
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy/workers     # 遗留工作线程
) 
