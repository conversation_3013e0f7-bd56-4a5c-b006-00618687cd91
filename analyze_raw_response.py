#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import struct
import binascii

def analyze_gzip_header(data):
    """分析gzip文件头"""
    if len(data) < 10:
        return "数据太短，无法分析gzip头"
    
    # gzip文件头格式
    magic = data[0:2]
    method = data[2]
    flags = data[3]
    mtime = struct.unpack('<I', data[4:8])[0]
    xfl = data[8]
    os_type = data[9]
    
    result = []
    result.append(f"Magic Number: {magic.hex()} ({'✅ 正确' if magic == b'\\x1f\\x8b' else '❌ 错误'})")
    result.append(f"压缩方法: {method} ({'Deflate' if method == 8 else '未知'})")
    result.append(f"标志位: {flags:08b}")
    result.append(f"修改时间: {mtime}")
    result.append(f"额外标志: {xfl} ({'最大压缩' if xfl == 2 else '最快压缩' if xfl == 4 else '默认'})")
    result.append(f"操作系统: {os_type}")
    
    return "\\n".join(result)

def analyze_gzip_trailer(data):
    """分析gzip文件尾"""
    if len(data) < 8:
        return "数据太短，无法分析gzip尾"
    
    # 最后8字节是CRC32和ISIZE
    trailer = data[-8:]
    crc32 = struct.unpack('<I', trailer[0:4])[0]
    isize = struct.unpack('<I', trailer[4:8])[0]
    
    return f"CRC32: {crc32:08x}\\nISIZE: {isize}字节 (原始数据大小)"

def find_gzip_data(data):
    """在数据中查找gzip压缩数据"""
    gzip_start = data.find(b'\\x1f\\x8b')
    if gzip_start == -1:
        return None, "未找到gzip数据"
    
    # 尝试解压来确定gzip数据的结束位置
    for end_pos in range(gzip_start + 18, len(data) + 1):
        try:
            gzip_data = data[gzip_start:end_pos]
            decompressed = gzip.decompress(gzip_data)
            return gzip_data, f"找到完整gzip数据: {len(gzip_data)}字节"
        except:
            continue
    
    return None, "找到gzip开始但无法确定结束位置"

def analyze_compression_level(original_data, compressed_data):
    """尝试推断压缩级别"""
    results = []
    
    for level in range(0, 10):
        test_compressed = gzip.compress(original_data, compresslevel=level)
        if test_compressed == compressed_data:
            return f"✅ 压缩级别: {level}"
        results.append(f"级别{level}: {len(test_compressed)}字节 (差异: {len(compressed_data) - len(test_compressed):+d})")
    
    return "❌ 无法确定压缩级别\\n" + "\\n".join(results)

def hex_dump(data, max_bytes=256):
    """十六进制转储"""
    lines = []
    for i in range(0, min(len(data), max_bytes), 16):
        chunk = data[i:i+16]
        hex_part = ' '.join(f'{b:02x}' for b in chunk)
        ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
        lines.append(f"{i:08x}: {hex_part:<48} |{ascii_part}|")
    
    if len(data) > max_bytes:
        lines.append(f"... (还有{len(data) - max_bytes}字节)")
    
    return "\\n".join(lines)

def analyze_response_structure(data, content_length):
    """分析响应结构"""
    result = []
    result.append(f"=== 响应结构分析 ===")
    result.append(f"总数据长度: {len(data)}字节")
    result.append(f"Content-Length声明: {content_length}字节")
    result.append(f"长度匹配: {'✅ 匹配' if len(data) == content_length else '❌ 不匹配'}")
    
    # 查找gzip数据
    gzip_data, gzip_info = find_gzip_data(data)
    result.append(f"\\nGzip数据: {gzip_info}")
    
    if gzip_data:
        gzip_start = data.find(gzip_data)
        gzip_end = gzip_start + len(gzip_data)
        
        result.append(f"Gzip位置: {gzip_start} - {gzip_end}")
        result.append(f"前置数据: {gzip_start}字节")
        result.append(f"后置数据: {len(data) - gzip_end}字节")
        
        # 分析前置数据
        if gzip_start > 0:
            prefix_data = data[:gzip_start]
            result.append(f"\\n=== 前置数据分析 ===")
            result.append(f"前置数据内容 (前64字节):")
            result.append(hex_dump(prefix_data, 64))
            
            # 尝试解析为ASCII
            try:
                ascii_part = prefix_data.decode('ascii', errors='ignore')
                if ascii_part.strip():
                    result.append(f"ASCII内容: {repr(ascii_part[:200])}")
            except:
                pass
        
        # 分析后置数据
        if gzip_end < len(data):
            suffix_data = data[gzip_end:]
            result.append(f"\\n=== 后置数据分析 ===")
            result.append(f"后置数据内容 (前64字节):")
            result.append(hex_dump(suffix_data, 64))
    
    return "\\n".join(result)

# 示例用法
def main():
    print("=== HTTP响应分析工具 ===")
    print("请提供以下信息:")
    print("1. HTTP响应头")
    print("2. 原始响应数据 (十六进制或base64)")
    print("3. 原始JSON明文")
    print()
    print("然后我将分析:")
    print("- 响应结构")
    print("- gzip压缩参数")
    print("- 压缩算法逆向推算")
    print("- 与明文的对应关系")

if __name__ == "__main__":
    main()
