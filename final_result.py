#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 新样本分析 - RecordCount=28, Content-Length=3114
json_size = 10462  # UTF-8字节
gzip_size = 1995   # 标准gzip压缩
content_length = 3114  # 服务器声明
record_count = 28  # 偶数

print("=== 新样本分析 ===")
print(f"JSON UTF-8大小: {json_size}字节")
print(f"gzip压缩大小: {gzip_size}字节")
print(f"服务器Content-Length: {content_length}字节")
print(f"RecordCount: {record_count} (偶数)")

# 计算包装开销
overhead = content_length - gzip_size
print(f"包装开销: {overhead}字节")
print(f"开销比例: {overhead/gzip_size:.1%}")

# 验证奇偶性理论
print(f"\n=== 奇偶性理论验证 ===")
if record_count % 2 == 0:
    predicted_range = "2900-3000字节"
    in_range = 2900 <= content_length <= 3000
else:
    predicted_range = "3100-3200字节"
    in_range = 3100 <= content_length <= 3200

print(f"预测范围: {predicted_range}")
print(f"实际结果: {content_length}字节")
if in_range:
    print("理论验证: ✅ 正确")
else:
    excess = content_length - 3000 if record_count % 2 == 0 else content_length - 3200
    print(f"理论验证: ❌ 错误 (超出{excess}字节)")

# 对比所有样本
print(f"\n=== 所有样本对比 ===")
all_samples = [
    {"records": 27, "content_length": 3154, "type": "奇数"},
    {"records": 31, "content_length": 3177, "type": "奇数"},
    {"records": 32, "content_length": 2960, "type": "偶数"},
    {"records": 33, "content_length": 3186, "type": "奇数"},
    {"records": 32, "content_length": 2932, "type": "偶数"},
    {"records": 31, "content_length": 3139, "type": "奇数"},
    {"records": 31, "content_length": 3111, "type": "奇数"},
    {"records": 31, "content_length": 3175, "type": "奇数"},
    {"records": 31, "content_length": 3093, "type": "奇数"},
    {"records": 28, "content_length": 3114, "type": "偶数"},  # 当前样本
]

print("所有样本:")
for i, sample in enumerate(all_samples, 1):
    print(f"  样本{i:2d}: RecordCount={sample['records']:2d} ({sample['type']}) → Content-Length={sample['content_length']}")

# 按奇偶性分组分析
even_samples = [s for s in all_samples if s['records'] % 2 == 0]
odd_samples = [s for s in all_samples if s['records'] % 2 == 1]

print(f"\n=== 分组统计 ===")
print(f"偶数RecordCount样本:")
even_lengths = [s['content_length'] for s in even_samples]
print(f"  Content-Length: {even_lengths}")
print(f"  范围: {min(even_lengths)}-{max(even_lengths)}字节")
print(f"  平均: {sum(even_lengths)/len(even_lengths):.0f}字节")

print(f"\n奇数RecordCount样本:")
odd_lengths = [s['content_length'] for s in odd_samples]
print(f"  Content-Length: {odd_lengths}")
print(f"  范围: {min(odd_lengths)}-{max(odd_lengths)}字节")
print(f"  平均: {sum(odd_lengths)/len(odd_lengths):.0f}字节")

# 最终结论
print(f"\n=== 最终结论 ===")
even_in_original_range = all(2900 <= cl <= 3000 for cl in even_lengths)
odd_in_original_range = all(3100 <= cl <= 3200 for cl in odd_lengths)

if even_in_original_range and odd_in_original_range:
    print("✅ 奇偶性理论完全正确")
elif even_in_original_range:
    print("⚠️ 偶数理论正确，奇数理论需要调整")
elif odd_in_original_range:
    print("⚠️ 奇数理论正确，偶数理论需要调整")
else:
    print("❌ 奇偶性理论需要重新考虑")

print(f"\n🎯 修正的预测范围:")
print(f"偶数RecordCount: {min(even_lengths)}-{max(even_lengths)}字节")
print(f"奇数RecordCount: {min(odd_lengths)}-{max(odd_lengths)}字节")
print(f"总体范围: {min(even_lengths + odd_lengths)}-{max(even_lengths + odd_lengths)}字节")

# 分析奇偶性的有效性
even_avg = sum(even_lengths) / len(even_lengths)
odd_avg = sum(odd_lengths) / len(odd_lengths)
avg_diff = abs(even_avg - odd_avg)

print(f"\n=== 奇偶性规律分析 ===")
print(f"偶数平均: {even_avg:.0f}字节")
print(f"奇数平均: {odd_avg:.0f}字节")
print(f"平均差异: {avg_diff:.0f}字节")

if avg_diff > 100:
    print("✅ 奇偶性确实有明显的分组效果")
    if even_avg < odd_avg:
        print("✅ 偶数RecordCount倾向于较小的Content-Length")
    else:
        print("✅ 奇数RecordCount倾向于较小的Content-Length")
else:
    print("❌ 奇偶性分组效果不明显")

print(f"\n🎯 对你的项目的最终建议:")
print(f"1. ✅ 基于Content-Length的接收策略完全正确")
print(f"2. ✅ 可以预期Content-Length在{min(even_lengths + odd_lengths)}-{max(even_lengths + odd_lengths)}字节范围")
print(f"3. ✅ 奇偶性有一定规律，偶数倾向于较小值")
print(f"4. ✅ 简单等待Content-Length是最可靠的方案")
print(f"5. ✅ 不需要复杂的预测逻辑")

print(f"\n🎯 实用的接收策略:")
print(f"```cpp")
print(f"// 简单有效的策略")
print(f"if (buffer.size() >= contentLength) {{")
print(f"    processGzipResponse();")
print(f"}}")
print(f"```")
