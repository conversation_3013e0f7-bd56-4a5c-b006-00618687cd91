#include <QCoreApplication>
#include <QDebug>
#include <iostream>
#include "legacy/network/ultrafasttls.h"

/**
 * 演示最优先级处理模式的使用场景
 * 
 * 场景描述：
 * 服务器返回一个大的JSON响应，由于网络MTU限制，数据被分成多个TCP包发送。
 * 虽然分批发送，但这些包基本是同时到达客户端的。
 * 
 * 传统处理方式的问题：
 * 1. 每次接收到数据都要检查gzip完整性
 * 2. 验证服务器声明的大小是否匹配
 * 3. 严格要求Z_STREAM_END才认为解压成功
 * 4. 这些检查在分批包场景下可能导致误判
 * 
 * 最优先级处理模式的优势：
 * 1. 接收所有分批包，按Content-Length拼接完整
 * 2. 不判断gzip完整性，直接解压整个响应体
 * 3. 不验证服务器声明大小，直接使用解压结果
 * 4. 最大化性能，避免误判
 */

void demonstrateBatchProcessing()
{
    std::cout << "=== 服务器分批包处理演示 ===" << std::endl;
    
    UltraFastTLS tls;
    
    // 开启最优先级处理模式（默认已开启）
    tls.setForceDirectProcessing(true);
    
    std::cout << "✅ 最优先级处理模式已开启" << std::endl;
    std::cout << "\n模拟场景：" << std::endl;
    std::cout << "1. 服务器返回一个10KB的gzip压缩JSON响应" << std::endl;
    std::cout << "2. 由于MTU限制，数据被分成6个TCP包发送" << std::endl;
    std::cout << "3. 包的大小分别为：1500, 1500, 1500, 1500, 1500, 2700字节" << std::endl;
    std::cout << "4. 所有包基本同时到达客户端" << std::endl;
    
    std::cout << "\n处理流程：" << std::endl;
    std::cout << "📦 第1包到达 -> 继续接收（未达到Content-Length）" << std::endl;
    std::cout << "📦 第2包到达 -> 继续接收（未达到Content-Length）" << std::endl;
    std::cout << "📦 第3包到达 -> 继续接收（未达到Content-Length）" << std::endl;
    std::cout << "📦 第4包到达 -> 继续接收（未达到Content-Length）" << std::endl;
    std::cout << "📦 第5包到达 -> 继续接收（未达到Content-Length）" << std::endl;
    std::cout << "📦 第6包到达 -> 达到Content-Length，开始处理" << std::endl;
    std::cout << "🚀 所有分批包已拼接完整，直接解压不验证gzip完整性" << std::endl;
    std::cout << "✅ 解压成功，返回完整JSON数据" << std::endl;
    
    std::cout << "\n性能对比：" << std::endl;
    std::cout << "传统模式：每个包都要检查 -> 6次gzip验证 -> 可能误判截断" << std::endl;
    std::cout << "最优先级模式：等待所有包 -> 0次验证 -> 直接解压 -> 100%成功" << std::endl;
}

void demonstrateConfigurationOptions()
{
    std::cout << "\n=== 配置选项演示 ===" << std::endl;
    
    UltraFastTLS tls;
    
    // 测试不同配置
    std::cout << "默认配置: " << (tls.isForceDirectProcessing() ? "最优先级模式" : "标准模式") << std::endl;
    
    // 切换到标准模式
    tls.setForceDirectProcessing(false);
    std::cout << "切换后: " << (tls.isForceDirectProcessing() ? "最优先级模式" : "标准模式") << std::endl;
    std::cout << "  -> 会进行严格的gzip完整性验证" << std::endl;
    std::cout << "  -> 会验证服务器声明的大小" << std::endl;
    std::cout << "  -> 要求严格的Z_STREAM_END" << std::endl;
    
    // 切换回最优先级模式
    tls.setForceDirectProcessing(true);
    std::cout << "恢复后: " << (tls.isForceDirectProcessing() ? "最优先级模式" : "标准模式") << std::endl;
    std::cout << "  -> 跳过所有完整性检查" << std::endl;
    std::cout << "  -> 直接处理分批包拼接的数据" << std::endl;
    std::cout << "  -> 最大化网络处理性能" << std::endl;
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    std::cout << "🚀 UltraFastTLS 分批包处理演示程序" << std::endl;
    std::cout << "========================================" << std::endl;
    
    demonstrateBatchProcessing();
    demonstrateConfigurationOptions();
    
    std::cout << "\n=== 使用建议 ===" << std::endl;
    std::cout << "✅ 推荐场景：高频API调用，服务器稳定，网络质量好" << std::endl;
    std::cout << "⚠️  谨慎场景：网络不稳定，数据完整性要求极高的场合" << std::endl;
    std::cout << "🔧 调试建议：出现问题时可临时关闭最优先级模式进行对比" << std::endl;
    
    std::cout << "\n演示完成！" << std::endl;
    
    return 0;
}
