﻿        return false;
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃幆 gzip瀹屾暣鎬ф鏌? 鏁版嵁瀹屾暣 (澹版槑澶у皬:%1瀛楄妭, 鍘嬬缉姣?%.2f)")
                .arg(declaredSize).arg(compressionRatio));

    return true;
}

// 瑙ｆ瀽HTTP鍝嶅簲锛屾彁鍙栧搷搴斾綋骞舵娴媖eep-alive
QString UltraFastTLS::parseHttpResponse(const QString& response, ConnectionInfo* connectionInfo)
{
    if (response.isEmpty()) {
        return QString();
    }

    // 鏌ユ壘HTTP澶寸粨鏉熶綅缃?    int headerEndPos = response.indexOf("\r\n\r\n");
    if (headerEndPos == -1) {
        return QString();
    }

    // 鎻愬彇HTTP澶村拰鍝嶅簲浣?    QString headers = response.left(headerEndPos);
    QString body = response.mid(headerEndPos + 4);

    // 妫€娴嬫湇鍔″櫒鏄惁澹版槑浜唊eep-alive
    if (connectionInfo) {
        connectionInfo->serverSaidKeepAlive = headers.contains("Connection: keep-alive", Qt::CaseInsensitive);

        // Keep-alive妫€娴嬶紙闈欓粯锛?    }

    // 妫€鏌TTP鐘舵€佺爜
    QStringList headerLines = headers.split("\r\n");
    if (headerLines.isEmpty()) {
        return QString();
    }

    // 妫€鏌ユ槸鍚︽槸chunked缂栫爜
    if (headers.contains("Transfer-Encoding: chunked", Qt::CaseInsensitive)) {
        body = decodeChunkedResponse(body);
    }

    // 馃攳 璇︾粏妫€鏌ユ暟鎹姸鎬侊紙gzip瑙ｅ帇宸插湪readHTTPResponse涓畬鎴愶級
    bool hasGzipHeader = headers.contains("Content-Encoding: gzip", Qt::CaseInsensitive);
    bool hasGzipMagic = false;

    if (body.length() >= 2) {
        QByteArray bodyBytes = body.toLatin1();
        unsigned char byte1 = (unsigned char)bodyBytes[0];
        unsigned char byte2 = (unsigned char)bodyBytes[1];
        hasGzipMagic = (byte1 == 0x1f && byte2 == 0x8b);
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 鏁版嵁鐘舵€? Header=%1, Magic=%2, 澶у皬=%3瀛楄妭")
                .arg(hasGzipHeader ? "gzip" : "鏃?)
                .arg(hasGzipMagic ? "鏄? : "鍚?)
                .arg(body.length()));

    // 馃攳 鍒嗘瀽HTTP澶撮儴淇℃伅
    QString contentLength, transferEncoding, contentType;
    for (const QString& line : headerLines) {
        if (line.startsWith("Content-Length:", Qt::CaseInsensitive)) {
            contentLength = line.mid(15).trimmed();
        } else if (line.startsWith("Transfer-Encoding:", Qt::CaseInsensitive)) {
            transferEncoding = line.mid(18).trimmed();
        } else if (line.startsWith("Content-Type:", Qt::CaseInsensitive)) {
            contentType = line.mid(13).trimmed();
        }
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 HTTP澶撮儴: Content-Length=%1, Transfer-Encoding=%2, Content-Type=%3")
                .arg(contentLength.isEmpty() ? "鏃? : contentLength)
                .arg(transferEncoding.isEmpty() ? "鏃? : transferEncoding)
                .arg(contentType.isEmpty() ? "鏃? : contentType));

    // 馃攳 妫€鏌ontent-Length涓庡疄闄呮暟鎹ぇ灏忕殑涓€鑷存€?    if (!contentLength.isEmpty()) {
        int declaredLength = contentLength.toInt();
        int actualLength = body.length();
        if (declaredLength != actualLength) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 Content-Length涓嶅尮閰? 澹版槑=%1瀛楄妭, 瀹為檯=%2瀛楄妭, 宸紓=%3瀛楄妭")
                           .arg(declaredLength).arg(actualLength).arg(declaredLength - actualLength));
        } else {
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?Content-Length鍖归厤: %1瀛楄妭").arg(actualLength));
        }
    }

    // 馃攳 妫€鏌zip鏁版嵁瀹屾暣鎬э紙鍦ㄨВ鍘嬪墠锛?    if (hasGzipHeader && hasGzipMagic) {
        QByteArray gzipBytes = body.toLatin1();
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip鍘熷鏁版嵁妫€鏌? 澶у皬=%1瀛楄妭, 寮€澶?%2, 缁撳熬=%3")
                    .arg(gzipBytes.size())
                    .arg(QString("%1 %2").arg((unsigned char)gzipBytes[0], 2, 16, QChar('0'))
                                         .arg((unsigned char)gzipBytes[1], 2, 16, QChar('0')))
                    .arg(QString("%1 %2").arg((unsigned char)gzipBytes[gzipBytes.size()-2], 2, 16, QChar('0'))
                                         .arg((unsigned char)gzipBytes[gzipBytes.size()-1], 2, 16, QChar('0'))));
    }

    // 馃敡 鎭㈠gzip瑙ｅ帇锛氬疄闄呬笂readHTTPResponse涓殑瑙ｅ帇娌℃湁琚皟鐢?    if (hasGzipHeader && hasGzipMagic) {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 寮€濮媑zip瑙ｅ帇澶勭悊");
        QByteArray bodyBytes = body.toLatin1();
        QString hexPreview;
        for (int i = 0; i < qMin(10, bodyBytes.length()); i++) {
            hexPreview += QString("%1 ").arg((unsigned char)bodyBytes[i], 2, 16, QChar('0'));
        }
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip澶撮儴: %1").arg(hexPreview));

        QString originalBody = body;

        // 馃敡 DEBUG妯″紡锛氳緭鍑哄畬鏁寸殑HTTP澶翠俊鎭?        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 瀹屾暣HTTP鍝嶅簲澶?===");
        QStringList headerLines = headers.split("\r\n");
        for (const QString& line : headerLines) {
            if (!line.trimmed().isEmpty()) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("Header: %1").arg(line));
            }
        }

        // 馃敡 DEBUG妯″紡锛氳緭鍑哄畬鏁寸殑鍘嬬缉鏁版嵁锛堝崄鍏繘鍒舵牸寮忥級
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 瀹屾暣鍘嬬缉鏁版嵁(HEX) ===");
        QString hexData;
        for (int i = 0; i < bodyBytes.size(); i++) {
            hexData += QString("%1").arg((unsigned char)bodyBytes[i], 2, 16, QChar('0'));
            if ((i + 1) % 32 == 0) hexData += "\n";
            else if ((i + 1) % 4 == 0) hexData += " ";
        }
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鍘嬬缉鏁版嵁澶у皬: %1瀛楄妭").arg(bodyBytes.size()));
        // 馃敡 涓嶅啀杈撳嚭瀹屾暣鍗佸叚杩涘埗鏁版嵁锛岄伩鍏嶆棩蹇楄繃闀?        QString hexTail = hexData.length() > 50 ? hexData.right(50) : hexData;
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鍘嬬缉鏁版嵁灏鹃儴: %1").arg(hexTail));

        // 馃敡 鎭㈠姝ｅ父瑙ｅ帇鍔熻兘
        body = decompressGzipResponse(body);

        // 馃攳 绮剧‘妫€鏌ユ暟鎹畬鏁存€?        QByteArray finalBodyBytes = body.toUtf8();
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃棞锔?gzip瑙ｅ帇: %1瀛楄妭 鈫?%2瀛楃 (%3瀛楄妭)")
                    .arg(originalBody.length()).arg(body.length()).arg(finalBodyBytes.size()));

        // 馃攳 妫€鏌SON瀹屾暣鎬у拰鏁版嵁涓€鑷存€?        if (body.length() > 10) {
            QString ending = body.right(10);
            bool endsWithBrace = body.endsWith("}");
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 鏁版嵁缁撳熬: '%1', 浠缁撳熬: %2")
                        .arg(ending).arg(endsWithBrace ? "鏄? : "鍚?));

            // 馃敡 鏄剧ず鍝嶅簲鏁版嵁鐨勬渶鍚?5涓瓧绗︼紙閬垮厤鏃ュ織杩囬暱锛?            QString dataTail = body.length() > 15 ? body.right(15) : body;
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 鍝嶅簲鏁版嵁灏鹃儴: %1").arg(dataTail));

            // 馃攳 妫€鏌String鍐呴儴涓€鑷存€?            QByteArray bodyAsBytes = body.toUtf8();
            if (bodyAsBytes.size() != finalBodyBytes.size()) {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 QString鏁版嵁涓嶄竴鑷? 鐩存帴杞崲=%1瀛楄妭, 鍘熷鏁版嵁=%2瀛楄妭")
                               .arg(bodyAsBytes.size()).arg(finalBodyBytes.size()));

                // 鏄剧ず涓や釜鐗堟湰鐨勭粨灏?                QString directEnding = QString::fromUtf8(bodyAsBytes.right(20));
                QString originalEnding = QString::fromUtf8(finalBodyBytes.right(20));
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("馃攳 鐩存帴缁撳熬: '%1'").arg(directEnding));
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("馃攳 鍘熷缁撳熬: '%1'").arg(originalEnding));

                // 馃敡 灏濊瘯浣跨敤鍘熷鏁版嵁閲嶆柊鏋勫缓QString
                if (!endsWithBrace && finalBodyBytes.size() > bodyAsBytes.size()) {
                    NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 灏濊瘯浣跨敤鍘熷鏁版嵁閲嶆柊鏋勫缓QString");
                    body = QString::fromUtf8(finalBodyBytes);
                    QString newEnding = body.right(10);
                    bool newEndsWithBrace = body.endsWith("}");
                    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 閲嶆瀯鍚庣粨灏? '%1', 浠缁撳熬: %2")
                                .arg(newEnding).arg(newEndsWithBrace ? "鏄? : "鍚?));
                }
            }
        }
    } else if (hasGzipHeader && !hasGzipMagic) {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?gzip鏁版嵁宸插湪鏃╂湡闃舵瑙ｅ帇瀹屾垚");
    } else {
        // 馃攳 鏄庢枃浼犺緭鐨勬儏鍐?        NEW_LOG_INFO(NewLogCategory::NETWORK, "馃搫 鏄庢枃浼犺緭妫€娴?);

        // 妫€鏌ユ槑鏂囨暟鎹殑瀹屾暣鎬?        if (body.length() > 10) {
            QString ending = body.right(10);
            bool endsWithBrace = body.endsWith("}");
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 鏄庢枃鏁版嵁缁撳熬: '%1', 浠缁撳熬: %2")
                        .arg(ending).arg(endsWithBrace ? "鏄? : "鍚?));

            // 馃敡 鏄剧ず鏄庢枃鏁版嵁鐨勬渶鍚?5涓瓧绗︼紙閬垮厤鏃ュ織杩囬暱锛?            QString dataTail = body.length() > 15 ? body.right(15) : body;
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 鏄庢枃鏁版嵁灏鹃儴: %1").arg(dataTail));

            // 妫€鏌ユ槑鏂嘕SON鏍煎紡
            if (body.trimmed().startsWith("{") && body.trimmed().endsWith("}")) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?鏄庢枃JSON鏍煎紡妫€鏌ラ€氳繃");
            } else {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 鏄庢枃JSON鏍煎紡鍙兘鏈夐棶棰?);
            }
        }
    }

    // 馃敟 绗竴姝ヨ皟璇曪細妫€鏌arseHttpResponse鐨勮繑鍥炲€?    qDebug() << "馃敟 parseHttpResponse鍗冲皢杩斿洖:" << body.length() << "瀛楃";
    if (body.length() > 0) {
        qDebug() << "馃敟 杩斿洖鍐呭棰勮:" << body.left(100) + "...";
        qDebug() << "馃敟 杩斿洖鍐呭灏鹃儴:" << body.right(20);
    } else {
        qDebug() << "馃敟 璀﹀憡锛氳繑鍥炲唴瀹逛负绌猴紒";
    }

    return body;
}

// 馃敡 JSON鏍煎紡淇鍑芥暟
QString UltraFastTLS::fixJsonFormat(const QString& jsonStr) {
    QString fixed = jsonStr;

    // 妫€鏌ユ槸鍚︿互{寮€澶?    if (!fixed.trimmed().startsWith("{")) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 JSON涓嶄互{寮€澶达紝灏濊瘯淇");
        return fixed; // 鏃犳硶淇
    }

    // 妫€鏌ユ槸鍚︿互}缁撳熬
    if (!fixed.trimmed().endsWith("}")) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 JSON涓嶄互}缁撳熬锛屽皾璇曟坊鍔犵粨灏?);

        // 灏濊瘯鎵惧埌鏈€鍚庝竴涓畬鏁寸殑瀛楁
        int lastComma = fixed.lastIndexOf(",");
        int lastColon = fixed.lastIndexOf(":");
        int lastQuote = fixed.lastIndexOf("\"");

        if (lastColon > lastComma && lastQuote > lastColon) {
            // 鐪嬭捣鏉ュ湪涓€涓瓧绗︿覆鍊间腑琚埅鏂?            fixed += "\"}";
            NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 娣诲姞浜嗗瓧绗︿覆缁撳熬鍜屽璞＄粨灏?);
        } else if (lastComma > lastColon) {
            // 鐪嬭捣鏉ュ湪瀛楁鍚嶆垨鍊间腑琚埅鏂紝绉婚櫎鏈€鍚庣殑閫楀彿骞舵坊鍔犵粨灏?            fixed = fixed.left(lastComma) + "}";
            NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 绉婚櫎浜嗕笉瀹屾暣瀛楁骞舵坊鍔犲璞＄粨灏?);
        } else {
            // 绠€鍗曟坊鍔犵粨灏?            fixed += "}";
            NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 绠€鍗曟坊鍔犱簡瀵硅薄缁撳熬");
        }
    }

    return fixed;
}

// 鎻愬彇Content-Length澶?int UltraFastTLS::extractContentLength(const QString& headers)
{
    QStringList headerLines = headers.split("\r\n");
    for (const QString& line : headerLines) {
        if (line.startsWith("Content-Length:", Qt::CaseInsensitive)) {
            QString lengthStr = line.mid(15).trimmed();
            bool ok;
            int length = lengthStr.toInt(&ok);
            return ok ? length : -1;
        }
    }
    return -1;
}

// 瑙ｇ爜chunked浼犺緭缂栫爜
QString UltraFastTLS::decodeChunkedResponse(const QString& chunkedData)
{
    // 馃敡 淇锛氫娇鐢↙atin1淇濇寔鍘熷瀛楄妭鏁版嵁锛岄伩鍏峌TF-8杞崲鐮村潖浜岃繘鍒舵暟鎹?    QByteArray data = chunkedData.toLatin1();
    QByteArray result;
    int pos = 0;

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 寮€濮媍hunked瑙ｇ爜: 杈撳叆澶у皬=%1瀛楄妭").arg(data.size()));

    while (pos < data.size()) {
        // 鏌ユ壘chunk澶у皬琛岀殑缁撴潫
        int crlfPos = data.indexOf("\r\n", pos);
        if (crlfPos == -1) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜: 鏈壘鍒癈RLF锛屼綅缃?%1").arg(pos));
            break;
        }

        // 瑙ｆ瀽chunk澶у皬锛堝崄鍏繘鍒讹級
        QByteArray chunkSizeBytes = data.mid(pos, crlfPos - pos);
        QString chunkSizeStr = QString::fromLatin1(chunkSizeBytes);
        bool ok;
        int chunkSize = chunkSizeStr.toInt(&ok, 16);
        if (!ok) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜: 鏃犳晥鐨刢hunk澶у皬 '%1'").arg(chunkSizeStr));
            break;
        }

        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 chunked: 瑙ｆ瀽chunk澶у皬=%1 (0x%2)").arg(chunkSize).arg(chunkSizeStr));

        if (chunkSize == 0) {
            // 鏈€鍚庝竴涓猚hunk
            NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?chunked瑙ｇ爜: 鍒拌揪缁撴潫chunk");
            break;
        }

        // 璺宠繃CRLF锛岃鍙朿hunk鏁版嵁
        pos = crlfPos + 2;
        if (pos + chunkSize > data.size()) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜: chunk鏁版嵁涓嶅畬鏁达紝闇€瑕?%1锛屽彲鐢?%2")
                           .arg(chunkSize).arg(data.size() - pos));
            break;
        }

        result.append(data.mid(pos, chunkSize));
        pos += chunkSize + 2; // 璺宠繃chunk鏁版嵁鍜岀粨灏剧殑CRLF

        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?chunked: 鎴愬姛瑙ｇ爜chunk锛屽ぇ灏?%1锛屾€昏=%2瀛楄妭")
                    .arg(chunkSize).arg(result.size()));
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 chunked瑙ｇ爜瀹屾垚: 杈撳嚭澶у皬=%1瀛楄妭").arg(result.size()));

    // 馃攳 妫€鏌ヨВ鐮佸悗鐨刧zip鏁版嵁瀹屾暣鎬?    QString decodedResult = QString::fromLatin1(result);
    if (result.size() >= 18 &&
        (unsigned char)result[0] == 0x1f &&
        (unsigned char)result[1] == 0x8b) {
        // 鏄痝zip鏁版嵁锛屾鏌ュ畬鏁存€?
        // 鎻愬彇澹版槑鐨勫師濮嬪ぇ灏忥紙鏈€鍚?瀛楄妭锛屽皬绔簭锛?        int declaredSize = 0;
        declaredSize |= (unsigned char)result[result.size()-4];
        declaredSize |= (unsigned char)result[result.size()-3] << 8;
        declaredSize |= (unsigned char)result[result.size()-2] << 16;
        declaredSize |= (unsigned char)result[result.size()-1] << 24;

        // 鍩烘湰鍚堢悊鎬ф鏌?        if (declaredSize > 0 && declaredSize <= 100*1024*1024) {
            double compressionRatio = (double)declaredSize / result.size();
            if (compressionRatio >= 0.1) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃幆 chunked瑙ｇ爜鍚巊zip瀹屾暣鎬ф鏌? 鏁版嵁瀹屾暣 (澹版槑澶у皬:%1瀛楄妭, 鍘嬬缉姣?%.2f)")
                            .arg(declaredSize).arg(compressionRatio));
            } else {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜鍚巊zip鍘嬬缉姣斿紓甯? %1").arg(compressionRatio));
            }
        } else {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜鍚巊zip澹版槑澶у皬寮傚父: %1瀛楄妭").arg(declaredSize));
        }
    }

    // 馃敡 淇锛氫娇鐢↙atin1杩斿洖锛屼繚鎸佷簩杩涘埗鏁版嵁瀹屾暣鎬?    return decodedResult;
}

// 瑙ｅ帇gzip鍝嶅簲
QString UltraFastTLS::decompressGzipResponse(const QString& gzipData)
{
#ifdef HAS_ZLIB
    // 馃敡 淇锛歡zip鏁版嵁鏄簩杩涘埗锛屼笉鑳界敤toUtf8()
    QByteArray compressed = gzipData.toLatin1();  // 淇濇寔鍘熷瀛楄妭
    QByteArray decompressed;

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 gzip瑙ｅ帇寮€濮? 杈撳叆澶у皬=%1瀛楄妭").arg(compressed.size()));

    // 馃攳 璇︾粏妫€鏌zip鏁版嵁瀹屾暣鎬?    if (compressed.size() >= 10) {
        QString hexHeader;
        for (int i = 0; i < qMin(10, compressed.size()); i++) {
            hexHeader += QString("%1 ").arg((unsigned char)compressed[i], 2, 16, QChar('0'));
        }
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip澶撮儴: %1").arg(hexHeader));

        // 馃攳 妫€鏌zip灏鹃儴锛堟渶鍚?瀛楄妭鍖呭惈CRC32鍜屽師濮嬪ぇ灏忥級
        if (compressed.size() >= 8) {
            QString tailHex;
            for (int i = compressed.size() - 8; i < compressed.size(); i++) {
                tailHex += QString("%1 ").arg((unsigned char)compressed[i], 2, 16, QChar('0'));
            }
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip灏鹃儴: %1").arg(tailHex));

            // 馃攳 浠巊zip灏鹃儴璇诲彇鍘熷澶у皬锛堝皬绔簭锛?            const unsigned char* tail = (const unsigned char*)compressed.data() + compressed.size() - 4;
            uint32_t originalSize = tail[0] | (tail[1] << 8) | (tail[2] << 16) | (tail[3] << 24);
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip澹版槑鐨勫師濮嬪ぇ灏? %1瀛楄妭").arg(originalSize));
        }

        if ((unsigned char)compressed[0] == 0x1f && (unsigned char)compressed[1] == 0x8b) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?gzip榄旀暟楠岃瘉閫氳繃 (1f 8b)");
        } else {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 gzip榄旀暟楠岃瘉澶辫触");
        }
    }

    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    stream.avail_in = compressed.size();
    stream.next_in = (Bytef*)compressed.data();

    // 鍒濆鍖杇zip瑙ｅ帇
    if (inflateInit2(&stream, 16 + MAX_WBITS) != Z_OK) {
        return gzipData; // 杩斿洖鍘熷鏁版嵁
    }

    char buffer[8192];
    int ret;
    do {
        stream.avail_out = sizeof(buffer);
        stream.next_out = (Bytef*)buffer;
        ret = inflate(&stream, Z_NO_FLUSH);
        if (ret == Z_STREAM_ERROR) break;
        decompressed.append(buffer, sizeof(buffer) - stream.avail_out);
    } while (stream.avail_out == 0);

    inflateEnd(&stream);

    // 馃敡 淇锛氭纭垽鏂В鍘嬬粨鏋?    if (ret == Z_STREAM_END || (ret == Z_OK && decompressed.size() > 0)) {
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?gzip瑙ｅ帇鎴愬姛: %1 鈫?%2瀛楄妭")
                    .arg(compressed.size()).arg(decompressed.size()));

        // 馃攳 楠岃瘉瑙ｅ帇缁撴灉涓巊zip澹版槑鐨勫ぇ灏忔槸鍚︿竴鑷?        if (compressed.size() >= 8) {
            const unsigned char* tail = (const unsigned char*)compressed.data() + compressed.size() - 4;
            uint32_t declaredSize = tail[0] | (tail[1] << 8) | (tail[2] << 16) | (tail[3] << 24);
            if (decompressed.size() == declaredSize) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?瑙ｅ帇澶у皬楠岃瘉閫氳繃: 瀹為檯=%1, 澹版槑=%2")
                            .arg(decompressed.size()).arg(declaredSize));
            } else {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 瑙ｅ帇澶у皬涓嶅尮閰? 瀹為檯=%1, 澹版槑=%2")
                               .arg(decompressed.size()).arg(declaredSize));
            }
        }

        // 馃攳 鏄剧ず瑙ｅ帇鍚庢暟鎹殑棰勮鍜屽畬鏁存€ф鏌?        QString preview = QString::fromUtf8(decompressed.left(100));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 瑙ｅ帇鍚庨瑙? %1").arg(preview));

        // 馃攳 妫€鏌SON鏍煎紡瀹屾暣鎬?        QString fullData = QString::fromUtf8(decompressed);
        if (fullData.startsWith("{") && fullData.endsWith("}")) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?JSON鏍煎紡妫€鏌ラ€氳繃 (浠}鍖呭洿)");
        } else {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 JSON鏍煎紡鍙兘鏈夐棶棰? 寮€澶?'%1', 缁撳熬='%2'")
                           .arg(fullData.left(5)).arg(fullData.right(5)));
        }

        // 馃敡 鏄剧ず瑙ｅ帇鏁版嵁鐨勬渶鍚?涓眽瀛楋紙閬垮厤鏃ュ織杩囬暱锛?        QString dataTail = fullData.length() > 15 ? fullData.right(15) : fullData;
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 瑙ｅ帇鏁版嵁灏鹃儴: %1").arg(dataTail));

        // 馃敡 鏅鸿兘缂栫爜绛栫暐锛氫紭鍏圲TF-8锛屽繀瑕佹椂鍥為€€鍒癓atin1
        QString result = QString::fromUtf8(decompressed);
        QByteArray backToBytes = result.toUtf8();

        if (backToBytes.size() == decompressed.size()) {
            // UTF-8杞崲瀹屾暣锛屼娇鐢║TF-8缁撴灉
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?UTF-8杞崲瀹屾暣: 鍘熷=%1瀛楄妭, 杞崲鍚?%2瀛楃")
                        .arg(decompressed.size()).arg(result.length()));
        } else {
            // UTF-8杞崲涓嶅畬鏁达紝鍥為€€鍒癓atin1
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 UTF-8杞崲鎴柇: 鍘熷=%1瀛楄妭, 鍥炶浆=%2瀛楄妭, 浣跨敤Latin1")
                           .arg(decompressed.size()).arg(backToBytes.size()));

            result = QString::fromLatin1(decompressed);
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 Latin1澶囩敤杞崲: 鍘熷=%1瀛楄妭, 杞崲鍚?%2瀛楃")
                        .arg(decompressed.size()).arg(result.length()));

            // 馃敡 灏濊瘯淇Latin1杞崲鍚庣殑JSON鏍煎紡闂
            result = fixJsonFormat(result);
        }

        return result;
    } else {
        NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("鉂?gzip瑙ｅ帇澶辫触: 閿欒鐮?%1, 瑙ｅ帇澶у皬=%2")
                     .arg(ret).arg(decompressed.size()));

        // 馃攳 灏濊瘯鍒嗘瀽澶辫触鍘熷洜
        if (ret == Z_DATA_ERROR) {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "鉂?鏁版嵁鎹熷潖鎴栨牸寮忛敊璇?);
        } else if (ret == Z_MEM_ERROR) {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "鉂?鍐呭瓨涓嶈冻");
        } else if (ret == Z_BUF_ERROR) {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "鉂?缂撳啿鍖洪敊璇?);
        }

        return gzipData; // 杩斿洖鍘熷鏁版嵁
    }
#else
    return gzipData; // 杩斿洖鍘熷鏁版嵁
#endif
}

// SSL閿欒鎻忚堪鍑芥暟
QString UltraFastTLS::getSSLErrorDescription(int sslError)
{
    switch (sslError) {
        case SSL_ERROR_NONE:
            return "鏃犻敊璇?;
        case SSL_ERROR_SSL:
            return "SSL鍗忚閿欒 - 鍙兘鏄瘉涔﹂棶棰樻垨鍗忚涓嶅尮閰?;
        case SSL_ERROR_WANT_READ:
            return "闇€瑕佹洿澶氭暟鎹鍙?;
        case SSL_ERROR_WANT_WRITE:
            return "闇€瑕佹洿澶氭暟鎹啓鍏?;
        case SSL_ERROR_WANT_X509_LOOKUP:
            return "X509璇佷功鏌ユ壘閿欒";
        case SSL_ERROR_SYSCALL:
            return "绯荤粺璋冪敤閿欒 - 鍙兘鏄綉缁滀腑鏂?;
        case SSL_ERROR_ZERO_RETURN:
            return "杩炴帴琚鏂规甯稿叧闂?;
        case SSL_ERROR_WANT_CONNECT:
            return "杩炴帴鏈畬鎴?;
        case SSL_ERROR_WANT_ACCEPT:
            return "鎺ュ彈鏈畬鎴?;
        default:
            return QString("鏈煡SSL閿欒 (%1)").arg(sslError);
    }
}

// 妫€鏌ヨ繛鎺ュ仴搴风姸鎬?(宸插垹闄?- 姘镐笉閲嶈繛瀹為獙)
bool UltraFastTLS::isConnectionHealthy(ConnectionInfo* /*conn*/)
{
    // 馃棏锔?鍋ュ悍妫€鏌ュ凡鍒犻櫎锛屾€绘槸杩斿洖true
    return true; // 鍋囪鎵€鏈夎繛鎺ラ兘鍋ュ悍
}

// 鏅鸿兘閲嶈瘯鏈哄埗
void UltraFastTLS::cleanupStaleConnections()
{
    // 杩炴帴娓呯悊宸插垹闄わ紝淇濇寔鎵€鏈夎繛鎺?}

QString UltraFastTLS::handleSmartRetry(const ParsedUrlInfo& /*parsedUrl*/, const QString& /*httpRequest*/)
{
    // 鏅鸿兘閲嶈瘯宸插垹闄?    return QString();
}

// 鍒嗘瀽杩炴帴鏂紑鍘熷洜
QString UltraFastTLS::analyzeDisconnectReason(ConnectionInfo* conn)
{
    if (!conn) {
        return "杩炴帴淇℃伅涓虹┖";
    }

    auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now() - conn->lastUsed).count();

    QString reason = "鏈嶅姟鍣ㄤ富鍔ㄦ柇寮€SSL杩炴帴锛屽師鍥犲垎鏋愶細";

    // 鍒嗘瀽鏂紑妯″紡
    if (conn->reuseCount == 1) {
        reason += QString("棣栨浣跨敤灏辨柇寮€锛屽彲鑳芥槸鏈嶅姟鍣ㄦ嫆缁濊繛鎺ュ鐢?);
    } else if (conn->reuseCount >= 100) {
        reason += QString("绗?1娆″鐢ㄨ鎷掔粷锛屾湇鍔″櫒鏈夊鐢ㄦ鏁伴檺鍒?).arg(conn->reuseCount);
    } else if (conn->reuseCount == 2) {
        reason += QString("绗?娆″鐢ㄥけ璐ワ紝鍙兘鏄綉缁滄尝鍔ㄦ垨鏈嶅姟鍣ㄨ礋杞?);
    } else {
        reason += QString("绗?1娆″鐢ㄥけ璐ワ紝寮傚父鏂紑").arg(conn->reuseCount);
    }

    // 鍒嗘瀽鏃堕棿鍥犵礌
    if (connectionAge > 30) {
        reason += QString("锛岃繛鎺ュ凡瀛樻椿%1绉掞紝鍙兘瓒呮椂").arg(connectionAge);
    } else if (connectionAge < 1) {
        reason += QString("锛岃繛鎺ュ垰寤虹珛%1绉掑氨鏂紑锛屾湇鍔″櫒蹇€熸嫆缁?).arg(connectionAge);
    }

    // 鍒嗘瀽Keep-Alive澹版槑
    if (conn->serverSaidKeepAlive) {
        reason += "锛屾湇鍔″櫒涔嬪墠澹版槑鏀寔keep-alive浣嗗疄闄呮柇寮€锛堣櫄鍋噆eep-alive锛?;
    } else {
        reason += "锛屾湇鍔″櫒鏈０鏄巏eep-alive鏀寔";
    }

    return reason;
}

// 鑾峰彇TCP杩炴帴鐘舵€?QString UltraFastTLS::getTcpConnectionState(ConnectionInfo* conn)
{
    if (!conn) {
        return "杩炴帴淇℃伅涓虹┖";
    }

    if (conn->socket == INVALID_SOCKET) {
        return "Socket鏃犳晥(宸插叧闂?";
    }

    // 绠€鍗曠殑杩炴帴鐘舵€佹娴?    QString stateStr;

    if (conn->serverClosed) {
        stateStr = "鏈嶅姟鍣ㄥ凡鍏抽棴杩炴帴";
    } else if (conn->isValid) {
        stateStr = "杩炴帴鏈夋晥";
    } else {
        stateStr = "杩炴帴宸叉爣璁颁负鏃犳晥";
    }

    // 娣诲姞杩炴帴淇℃伅
    stateStr += QString(" (澶嶇敤%1娆?").arg(conn->reuseCount);

    if (conn->serverSaidKeepAlive) {
        stateStr += " [澹版槑Keep-Alive]";
    }

    return stateStr;
}

// 妫€娴嬫湇鍔″櫒SSL澶嶈繛鏀寔鑳藉姏 (宸插垹闄?- 姘镐笉閲嶈繛瀹為獙)
QString UltraFastTLS::detectSSLReconnectSupport(ConnectionInfo* /*conn*/)
{
    // 馃棏锔?SSL妫€娴嬪凡鍒犻櫎锛岀洿鎺ヨ繑鍥炵畝鍗曚俊鎭?    return "馃棏锔?SSL妫€娴嬪凡鍒犻櫎 - 姘镐笉閲嶈繛瀹為獙";
}

// 浠ｇ悊鏀寔 - 楂樻€ц兘鐗堟湰
void UltraFastTLS::setProxy(const QString& host, int port, const QString& type,
                           const QString& user, const QString& pass)
{
    QMutexLocker locker(&m_poolMutex);

    // 妫€鏌ヤ唬鐞嗛厤缃槸鍚︾湡鐨勫彂鐢熶簡鍙樺寲
    bool configChanged = (m_proxyConfig.host != host ||
                         m_proxyConfig.port != port ||
                         m_proxyConfig.type != type.toLower() ||
                         m_proxyConfig.user != user ||
                         m_proxyConfig.pass != pass);

    if (!configChanged) {
        // 閰嶇疆娌℃湁鍙樺寲锛岀洿鎺ヨ繑鍥烇紝閬垮厤涓嶅繀瑕佺殑杩炴帴姹犳竻鐞?        return;
    }

    m_proxyConfig.host = host;
    m_proxyConfig.port = port;
    m_proxyConfig.type = type.toLower();
    m_proxyConfig.user = user;
    m_proxyConfig.pass = pass;
    m_proxyConfig.enabled = !host.isEmpty() && port > 0;

    if (!m_quietMode) {
        if (m_proxyConfig.enabled) {
            emit debugLog(QString("馃敆 UltraFastTLS浠ｇ悊宸茶缃? %1:%2 (%3)")
                         .arg(host).arg(port).arg(type));
        } else {
            emit debugLog("馃敆 UltraFastTLS浠ｇ悊宸茬鐢?);
        }
    }

    // 浠ｇ悊閰嶇疆鍙樻洿鏃舵竻鐞嗚繛鎺ユ睜锛堥珮鎬ц兘锛氬彧娓呯悊褰撳墠瀹炰緥鐨勮繛鎺ワ級
    cleanupAllConnections();
}

void UltraFastTLS::clearProxy()
{
    QMutexLocker locker(&m_poolMutex);

    m_proxyConfig.enabled = false;
    m_proxyConfig.host.clear();
    m_proxyConfig.port = 0;
    m_proxyConfig.user.clear();
    m_proxyConfig.pass.clear();

    if (!m_quietMode) {
        emit debugLog("馃敆 UltraFastTLS浠ｇ悊閰嶇疆宸叉竻闄?);
    }

    // 娓呯悊鐜版湁杩炴帴
    cleanupAllConnections();
}

bool UltraFastTLS::hasProxy() const
{
    QMutexLocker locker(&m_poolMutex);
    return m_proxyConfig.enabled;
}

// 浠嶩TTP璇锋眰涓彁鍙朠OST鏁版嵁
QString UltraFastTLS::extractPostDataFromRequest(const QString& httpRequest)
{
    // 鏌ユ壘HTTP澶撮儴鍜屾鏂囩殑鍒嗛殧绗?    int separatorPos = httpRequest.indexOf("\r\n\r\n");
    if (separatorPos == -1) {
        return QString();
    }

    // 杩斿洖姝ｆ枃閮ㄥ垎
    return httpRequest.mid(separatorPos + 4);
}

