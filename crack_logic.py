#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 第五个样本 - Content-Length: 2932字节
json_data = '''{"LevelOrderList":[{"px":1,"UserID":5993179,"SerialNo":"11017648883937125814","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 6s20a","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":18.56},{"px":2,"UserID":23708003,"SerialNo":"11017650214830231905","IsPub":1,"ZoneServerID":"110104017182700","Title":"别接破单了█招聘优质打手█价格相对较高!看代练要求!","Price":2.0000,"Ensure1":1.0000,"Ensure2":0.0000,"Ensure":1.0000,"TimeLimit":2400,"Stamp":0,"Create":"肥单▉招▉打▉手91","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00}],"RecordCount":32,"HasNew":0}'''

print("=== 破解ASP.NET分配逻辑 ===")

# 五个样本的完整数据
samples = [
    {
        "name": "样本1",
        "json_size": 10471,
        "content_length": 3154,
        "records": 27,
        "first_userid": 23700204,
        "last_userid": 5993179
    },
    {
        "name": "样本2", 
        "json_size": 2389,
        "content_length": 3177,
        "records": 31,
        "first_userid": 5491031,
        "last_userid": 5376429
    },
    {
        "name": "样本3",
        "json_size": 8500,  # 估算
        "content_length": 2960,
        "records": 32,
        "first_userid": 13924284,
        "last_userid": 5491031
    },
    {
        "name": "样本4",
        "json_size": 2989,
        "content_length": 3186,
        "records": 33,
        "first_userid": 5376429,
        "last_userid": 18205529
    },
    {
        "name": "样本5",
        "json_size": len(json_data.encode('utf-8')),
        "content_length": 2932,
        "records": 32,
        "first_userid": 5993179,
        "last_userid": 5491031
    }
]

print("=== 五个样本完整对比 ===")
for sample in samples:
    print(f"{sample['name']}: JSON={sample['json_size']}字节, "
          f"Content-Length={sample['content_length']}字节, "
          f"记录数={sample['records']}, "
          f"首UserID={sample['first_userid']}")

# 分析Content-Length的分布
content_lengths = [s['content_length'] for s in samples]
print(f"\n=== Content-Length分布 ===")
print(f"所有值: {content_lengths}")
print(f"最小值: {min(content_lengths)}字节")
print(f"最大值: {max(content_lengths)}字节")
print(f"变化范围: {max(content_lengths) - min(content_lengths)}字节")

# 寻找分组模式
print(f"\n=== 寻找分组模式 ===")

# 按Content-Length分组
groups = {}
for sample in samples:
    cl = sample['content_length']
    range_key = None
    if 2900 <= cl <= 3000:
        range_key = "2900-3000"
    elif 3100 <= cl <= 3200:
        range_key = "3100-3200"
    
    if range_key:
        if range_key not in groups:
            groups[range_key] = []
        groups[range_key].append(sample)

for range_name, group_samples in groups.items():
    print(f"\n{range_name}字节范围:")
    for sample in group_samples:
        print(f"  {sample['name']}: 记录数={sample['records']}, 首UserID={sample['first_userid']}")

# 测试各种假设
print(f"\n=== 测试决策逻辑 ===")

# 假设1: 基于记录数奇偶性
print("假设1: 基于记录数奇偶性")
for sample in samples:
    parity = "偶数" if sample['records'] % 2 == 0 else "奇数"
    range_pred = "2900-3000" if sample['records'] % 2 == 0 else "3100-3200"
    actual_range = "2900-3000" if 2900 <= sample['content_length'] <= 3000 else "3100-3200"
    match = "✅" if range_pred == actual_range else "❌"
    print(f"  {sample['name']}: 记录数={sample['records']}({parity}) → 预测{range_pred} vs 实际{actual_range} {match}")

# 假设2: 基于首个UserID的特征
print("\n假设2: 基于首个UserID的特征")
for sample in samples:
    userid_mod = sample['first_userid'] % 1000000
    range_pred = "2900-3000" if userid_mod < 500000 else "3100-3200"
    actual_range = "2900-3000" if 2900 <= sample['content_length'] <= 3000 else "3100-3200"
    match = "✅" if range_pred == actual_range else "❌"
    print(f"  {sample['name']}: UserID={sample['first_userid']}(mod={userid_mod}) → 预测{range_pred} vs 实际{actual_range} {match}")

# 假设3: 基于记录数的特定模运算
print("\n假设3: 基于记录数模4")
for sample in samples:
    mod4 = sample['records'] % 4
    range_pred = "2900-3000" if mod4 == 0 else "3100-3200"
    actual_range = "2900-3000" if 2900 <= sample['content_length'] <= 3000 else "3100-3200"
    match = "✅" if range_pred == actual_range else "❌"
    print(f"  {sample['name']}: 记录数={sample['records']}(mod4={mod4}) → 预测{range_pred} vs 实际{actual_range} {match}")

# 假设4: 基于JSON大小范围
print("\n假设4: 基于JSON大小范围")
for sample in samples:
    size_range = "小" if sample['json_size'] < 5000 else "大"
    range_pred = "2900-3000" if sample['json_size'] < 5000 else "3100-3200"
    actual_range = "2900-3000" if 2900 <= sample['content_length'] <= 3000 else "3100-3200"
    match = "✅" if range_pred == actual_range else "❌"
    print(f"  {sample['name']}: JSON={sample['json_size']}字节({size_range}) → 预测{range_pred} vs 实际{actual_range} {match}")

# 寻找完美匹配的逻辑
print(f"\n=== 寻找完美匹配逻辑 ===")

# 复合条件测试
print("测试复合条件:")

# 条件1: 记录数为32的特殊处理
condition1_matches = 0
for sample in samples:
    if sample['records'] == 32:
        pred_range = "2900-3000"
    else:
        pred_range = "3100-3200"
    
    actual_range = "2900-3000" if 2900 <= sample['content_length'] <= 3000 else "3100-3200"
    match = pred_range == actual_range
    if match:
        condition1_matches += 1
    
    print(f"  条件1 - {sample['name']}: 记录数={sample['records']} → 预测{pred_range} vs 实际{actual_range} {'✅' if match else '❌'}")

print(f"条件1匹配率: {condition1_matches}/{len(samples)} = {condition1_matches/len(samples)*100:.1f}%")

# 如果找到完美匹配，输出最终逻辑
if condition1_matches == len(samples):
    print(f"\n🎯 找到完美匹配逻辑!")
    print(f"ASP.NET分配逻辑:")
    print(f"if (recordCount == 32) {{")
    print(f"    return 2900-3000字节范围;")
    print(f"}} else {{")
    print(f"    return 3100-3200字节范围;")
    print(f"}}")

print(f"\n=== 当前最佳假设 ===")
print(f"基于现有数据，最可能的逻辑是基于记录数的特定值进行分组")
print(f"需要更多样本来验证和完善这个逻辑")
