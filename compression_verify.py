#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip

# 新样本 - RecordCount=28, Content-Length=待测试
json_data = '''{"LevelOrderList":[{"px":2,"UserID":5491031,"SerialNo":"11017650228913806419","IsPub":1,"ZoneServerID":"110104017182700","Title":"中忍3阶-影级1阶 6s15a","Price":4.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":20,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":9.76},{"px":3,"UserID":5993179,"SerialNo":"11017648444911673393","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3 36分-超影2500分 指定宇智波佐助 春野樱 漩涡鸣人","Price":66.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":80,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":46.81},{"px":4,"UserID":23705152,"SerialNo":"11017648253291080683","IsPub":1,"ZoneServerID":"110104017182700","Title":"【指定永恒佐助一局35块】","Price":3.0000,"Ensure1":60.0000,"Ensure2":60.0000,"Ensure":120.0000,"TimeLimit":50,"Stamp":0,"Create":"新手USR2025080407066","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":""},{"px":5,"UserID":5491031,"SerialNo":"11017650192159371805","IsPub":1,"ZoneServerID":"110104017182700","Title":"上忍1阶-暗部3阶 10s15a","Price":2.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":15,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":7.69},{"px":6,"UserID":7032583,"SerialNo":"11017649234449896060","IsPub":1,"ZoneServerID":"110104317255600","Title":"一个月日常加周胜，秘境探险跟团本，每天日常能搞的都要搞","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":2400,"Stamp":0,"Create":"ε?(?> ? <)?","SameCity":"","SettleHour":48,"SitePrice":"","Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":7,"UserID":5993179,"SerialNo":"11017649596870325924","IsPub":1,"ZoneServerID":"110104017182700","Title":"扫码 暗部1-影级1 3s 9a","Price":3.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":15,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":10.00},{"px":8,"UserID":23468051,"SerialNo":"11017644863363988536","IsPub":1,"ZoneServerID":"110104117222100","Title":"有需要打V团本的老板dd","Price":2.0000,"Ensure1":2.0000,"Ensure2":2.0000,"Ensure":4.0000,"TimeLimit":24,"Stamp":0,"Create":"新手USR2025071205302","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":9,"UserID":13924284,"SerialNo":"11017649935881854223","IsPub":1,"ZoneServerID":"110104217241000","Title":"指定冰墩墩，套餐1打90天","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2160,"Stamp":0,"Create":"小喇叭发单","SameCity":"","SettleHour":1,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":10,"UserID":18205529,"SerialNo":"11017650237796552881","IsPub":1,"ZoneServerID":"110104017182700","Title":"影级1阶-超影2500分【8S，17A】","Price":20.0000,"Ensure1":15.0000,"Ensure2":10.0000,"Ensure":25.0000,"TimeLimit":34,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":19.61},{"px":11,"UserID":5874699,"SerialNo":"11017613436873156575","IsPub":1,"ZoneServerID":"110104317255600","Title":"中忍2到超影（没实力别来，中途退，超时0结算）","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":3,"Stamp":0,"Create":"记得多喝热水。","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":13.64},{"px":12,"UserID":21716850,"SerialNo":"11017638838775995216","IsPub":1,"ZoneServerID":"110104217241000","Title":"苹果 影1到超 月初难打来个26选手 中途取消或接单不联系我扣钱","Price":14.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":5,"Stamp":0,"Create":"小河发单秒验收","SameCity":"","SettleHour":19,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.90},{"px":13,"UserID":5491031,"SerialNo":"11017650238018992034","IsPub":1,"ZoneServerID":"110104117222100","Title":"暗部1阶-超影2700分 11s30a","Price":43.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":44,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":19.20},{"px":14,"UserID":5993179,"SerialNo":"11017649643981411737","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍1-影级1  2S 13A","Price":5.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":27,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":12.82},{"px":15,"UserID":5376429,"SerialNo":"11017641628995604521","IsPub":1,"ZoneServerID":"110104317255600","Title":"【影1 52分-超影】 配置低来大佬 1s4a 战绩好超时不计","Price":16.0000,"Ensure1":15.0000,"Ensure2":15.0000,"Ensure":30.0000,"TimeLimit":24,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":47,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.39},{"px":17,"UserID":5993179,"SerialNo":"11017648444157533816","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3-超影  5s8a","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":52,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.00},{"px":18,"UserID":18205529,"SerialNo":"11017650162378137164","IsPub":1,"ZoneServerID":"110104017182700","Title":"★ 暗部3阶-超影1】3s10a/接单电话通知","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":50,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.50},{"px":19,"UserID":22049761,"SerialNo":"11018267638501169537","IsPub":1,"ZoneServerID":"110104017182700","Title":"更多玩法全部宝箱＋超影","Price":2.0000,"Ensure1":25.0000,"Ensure2":25.0000,"Ensure":50.0000,"TimeLimit":2,"Stamp":2097547561,"Create":"新手USR2025011103301","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":20,"UserID":5376429,"SerialNo":"11017643783357962828","IsPub":1,"ZoneServerID":"110104017182700","Title":"【武当花火忍道分1622分到2250分】 角色名后是号主手机 联系号主扫码上号","Price":20.0000,"Ensure1":14.0000,"Ensure2":14.0000,"Ensure":28.0000,"TimeLimit":30,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":46,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":28.30}],"RecordCount":28,"HasNew":0}'''

print("=== 实际压缩验证 ===")

utf8_data = json_data.encode('utf-8')
server_content_length = 3114  # 实际结果

print(f"JSON UTF-8大小: {len(utf8_data)}字节")
print(f"RecordCount: 28 (偶数)")
print(f"服务器Content-Length: {server_content_length}字节")

# 测试不同压缩级别
print(f"\n--- 测试不同gzip压缩级别 ---")
for level in range(0, 10):
    compressed = gzip.compress(utf8_data, compresslevel=level)
    diff = server_content_length - len(compressed)
    print(f"级别{level}: {len(compressed)}字节 (差异:{diff:+d}字节)")

# 使用标准级别6
compressed = gzip.compress(utf8_data, compresslevel=6)
diff = server_content_length - len(compressed)

print(f"\n--- 实际分析 ---")
print(f"标准gzip(级别6): {len(compressed)}字节")
print(f"服务器Content-Length: {server_content_length}字节")
print(f"包装开销: {diff}字节")

if len(compressed) > 0:
    ratio = diff / len(compressed)
    print(f"开销比例: {ratio:.1%}")

print(f"\n--- 理论验证 ---")
record_count = 28

# 验证奇偶性理论
if record_count % 2 == 0:
    predicted_range = "2900-3000字节"
    in_range = 2900 <= server_content_length <= 3000
else:
    predicted_range = "3100-3200字节"
    in_range = 3100 <= server_content_length <= 3200

print(f"奇偶性理论预测: {predicted_range}")
print(f"实际结果: {server_content_length}字节")
print(f"奇偶性理论: {'✅ 正确' if in_range else '❌ 错误'}")

# 对比之前的包装开销
previous_overhead = 2508  # 之前样本的开销
overhead_diff = abs(diff - previous_overhead)
print(f"\n包装开销对比:")
print(f"之前样本开销: {previous_overhead}字节")
print(f"当前样本开销: {diff}字节")
print(f"开销差异: {overhead_diff}字节")
print(f"开销一致性: {'✅ 相似' if overhead_diff < 500 else '❌ 差异较大'}")

print(f"\n=== 最终结论 ===")
if in_range and overhead_diff < 500:
    print("🎯 两个理论都基本正确")
elif in_range:
    print("🎯 奇偶性理论正确，但包装开销有变化")
elif overhead_diff < 500:
    print("🎯 包装开销一致，但奇偶性理论需要修正")
else:
    print("🎯 两个理论都需要重新考虑")
    print("可能存在更复杂的分配逻辑")
