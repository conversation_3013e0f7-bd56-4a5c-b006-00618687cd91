#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip

print("=== 固定大小响应理论验证 ===")

# 两个样本的数据
samples = [
    {
        "name": "样本1",
        "json_size": 10471,
        "content_length": 3154,
        "records": 27,
        "gzip_estimated": 2326
    },
    {
        "name": "样本2", 
        "json_size": 2389,
        "content_length": 3177,
        "records": 31,
        "gzip_estimated": 852
    }
]

print("数据对比:")
for sample in samples:
    print(f"{sample['name']}: JSON={sample['json_size']}字节, "
          f"Content-Length={sample['content_length']}字节, "
          f"记录数={sample['records']}")

# 计算固定大小的证据
content_lengths = [s['content_length'] for s in samples]
avg_content_length = sum(content_lengths) / len(content_lengths)
max_diff = max(content_lengths) - min(content_lengths)

print(f"\n=== 固定大小分析 ===")
print(f"平均Content-Length: {avg_content_length:.1f}字节")
print(f"最大差异: {max_diff}字节")
print(f"差异百分比: {(max_diff/avg_content_length)*100:.2f}%")

if max_diff < 50:
    print("✅ 强烈证据表明使用固定大小响应！")
elif max_diff < 100:
    print("✅ 很可能使用固定大小响应")
else:
    print("❌ 不是固定大小响应")

# 分析固定大小的构成
print(f"\n=== 固定大小构成分析 ===")
FIXED_SIZE = int(avg_content_length)
print(f"推测的固定大小: {FIXED_SIZE}字节")

print(f"\n各样本的空间利用:")
for sample in samples:
    gzip_size = sample['gzip_estimated']
    content_length = sample['content_length']
    
    # 估算头部大小
    header_size = 200  # 估算的固定头部
    
    # 计算数据区域
    data_area = content_length - header_size
    gzip_ratio = gzip_size / data_area if data_area > 0 else 0
    padding = data_area - gzip_size if data_area > gzip_size else 0
    
    print(f"{sample['name']}:")
    print(f"  固定头部: {header_size}字节")
    print(f"  数据区域: {data_area}字节")
    print(f"  gzip数据: {gzip_size}字节 ({gzip_ratio*100:.1f}%)")
    print(f"  填充数据: {padding}字节")

# 预测公式
print(f"\n=== 新的预测公式 ===")
print(f"Content-Length = {FIXED_SIZE}字节 (固定值)")
print(f"不管JSON多大，响应大小都约为{FIXED_SIZE}字节")

# 验证理论
print(f"\n=== 理论验证 ===")
print(f"如果这个理论正确，那么:")
print(f"1. 所有响应的Content-Length都应该接近{FIXED_SIZE}字节")
print(f"2. JSON越小，填充数据越多")
print(f"3. JSON越大，填充数据越少")
print(f"4. 当JSON压缩后超过数据区域时，可能会动态扩展")

# 实际应用建议
print(f"\n=== 实际应用建议 ===")
print(f"基于这个发现，你的接收逻辑应该:")
print(f"1. 期望Content-Length约为{FIXED_SIZE}字节")
print(f"2. 当接收到{FIXED_SIZE}字节时，尝试解压")
print(f"3. 在固定大小的数据中查找gzip部分")
print(f"4. 忽略填充数据")

# 模拟固定大小响应的结构
print(f"\n=== 模拟响应结构 ===")
print(f"┌─────────────────────────────────────────┐")
print(f"│ 固定头部 (200字节)                        │")
print(f"├─────────────────────────────────────────┤")
print(f"│ gzip数据 (变长: 800-2300字节)             │")
print(f"├─────────────────────────────────────────┤")
print(f"│ 填充数据 (变长: 600-2100字节)             │")
print(f"├─────────────────────────────────────────┤")
print(f"│ 固定尾部 (可选)                          │")
print(f"└─────────────────────────────────────────┘")
print(f"总大小: {FIXED_SIZE}字节 (固定)")

print(f"\n🎯 这解释了为什么Content-Length几乎不变！")
print(f"✅ ASP.NET使用固定大小的响应块来优化传输！")
