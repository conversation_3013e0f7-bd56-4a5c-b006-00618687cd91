#include <QCoreApplication>
#include <QDebug>
#include <QByteArray>
#include <QString>
#include <QFile>
#include <QTextStream>
#include <zlib.h>

class GzipCompressor {
public:
    // 模拟服务器的gzip压缩
    static QByteArray compressLikeServer(const QString& jsonData) {
        // 1. 转换为UTF-8字节数组（模拟服务器行为）
        QByteArray utf8Data = jsonData.toUtf8();
        qDebug() << "原始JSON UTF-8大小:" << utf8Data.size() << "字节";
        
        // 2. 使用zlib进行gzip压缩（模拟ASP.NET的压缩）
        QByteArray compressed = gzipCompress(utf8Data);
        
        if (compressed.isEmpty()) {
            qDebug() << "❌ 压缩失败";
            return QByteArray();
        }
        
        // 3. 验证压缩结果
        uint32_t isize = extractISIZE(compressed);
        qDebug() << "压缩后大小:" << compressed.size() << "字节";
        qDebug() << "ISIZE字段:" << isize << "字节";
        qDebug() << "ISIZE匹配:" << (isize == utf8Data.size() ? "✅" : "❌");
        
        return compressed;
    }
    
    // 使用zlib进行gzip压缩
    static QByteArray gzipCompress(const QByteArray& data) {
        if (data.isEmpty()) return QByteArray();
        
        // 分配输出缓冲区
        uLongf compressedSize = compressBound(data.size()) + 18; // gzip头部+尾部
        QByteArray compressed(compressedSize, 0);
        
        // 使用zlib的gzip压缩
        z_stream stream;
        stream.zalloc = Z_NULL;
        stream.zfree = Z_NULL;
        stream.opaque = Z_NULL;
        stream.avail_in = data.size();
        stream.next_in = (Bytef*)data.constData();
        stream.avail_out = compressed.size();
        stream.next_out = (Bytef*)compressed.data();
        
        // 初始化gzip压缩（使用默认压缩级别6，模拟服务器）
        int ret = deflateInit2(&stream, Z_DEFAULT_COMPRESSION, Z_DEFLATED, 
                              15 + 16, 8, Z_DEFAULT_STRATEGY); // 15+16 = gzip格式
        
        if (ret != Z_OK) {
            qDebug() << "❌ deflateInit2失败:" << ret;
            return QByteArray();
        }
        
        // 执行压缩
        ret = deflate(&stream, Z_FINISH);
        if (ret != Z_STREAM_END) {
            deflateEnd(&stream);
            qDebug() << "❌ deflate失败:" << ret;
            return QByteArray();
        }
        
        // 获取实际压缩大小
        uLongf actualSize = stream.total_out;
        deflateEnd(&stream);
        
        // 调整数组大小
        compressed.resize(actualSize);
        return compressed;
    }
    
    // 提取gzip的ISIZE字段
    static uint32_t extractISIZE(const QByteArray& gzipData) {
        if (gzipData.size() < 4) return 0;
        
        // ISIZE是最后4个字节，小端序
        uint32_t isize = 0;
        isize |= (unsigned char)gzipData[gzipData.size()-4];
        isize |= (unsigned char)gzipData[gzipData.size()-3] << 8;
        isize |= (unsigned char)gzipData[gzipData.size()-2] << 16;
        isize |= (unsigned char)gzipData[gzipData.size()-1] << 24;
        
        return isize;
    }
    
    // 解压gzip数据
    static QString decompressGzip(const QByteArray& gzipData) {
        if (gzipData.size() < 18) return QString();
        
        // 检查gzip魔数
        if ((unsigned char)gzipData[0] != 0x1F || (unsigned char)gzipData[1] != 0x8B) {
            qDebug() << "❌ 不是有效的gzip数据";
            return QString();
        }
        
        // 获取原始大小
        uint32_t originalSize = extractISIZE(gzipData);
        if (originalSize == 0 || originalSize > 100*1024*1024) {
            qDebug() << "❌ ISIZE异常:" << originalSize;
            return QString();
        }
        
        // 分配解压缓冲区
        QByteArray decompressed(originalSize, 0);
        
        // 使用zlib解压
        z_stream stream;
        stream.zalloc = Z_NULL;
        stream.zfree = Z_NULL;
        stream.opaque = Z_NULL;
        stream.avail_in = gzipData.size();
        stream.next_in = (Bytef*)gzipData.constData();
        stream.avail_out = decompressed.size();
        stream.next_out = (Bytef*)decompressed.data();
        
        // 初始化gzip解压
        int ret = inflateInit2(&stream, 15 + 16); // 15+16 = gzip格式
        if (ret != Z_OK) {
            qDebug() << "❌ inflateInit2失败:" << ret;
            return QString();
        }
        
        // 执行解压
        ret = inflate(&stream, Z_FINISH);
        if (ret != Z_STREAM_END) {
            inflateEnd(&stream);
            qDebug() << "❌ inflate失败:" << ret;
            return QString();
        }
        
        inflateEnd(&stream);
        
        // 转换为QString
        return QString::fromUtf8(decompressed);
    }
    
    // 验证压缩完整性
    static bool verifyCompression(const QByteArray& gzipData, const QString& originalJson) {
        qDebug() << "\n=== 压缩完整性验证 ===";
        
        // 1. 解压数据
        QString decompressed = decompressGzip(gzipData);
        if (decompressed.isEmpty()) {
            qDebug() << "❌ 解压失败";
            return false;
        }
        
        // 2. 比较内容
        if (decompressed != originalJson) {
            qDebug() << "❌ 解压内容与原始内容不匹配";
            qDebug() << "原始长度:" << originalJson.length();
            qDebug() << "解压长度:" << decompressed.length();
            return false;
        }
        
        // 3. 验证ISIZE
        uint32_t isize = extractISIZE(gzipData);
        uint32_t actualSize = originalJson.toUtf8().size();
        
        if (isize != actualSize) {
            qDebug() << "❌ ISIZE验证失败";
            qDebug() << "ISIZE:" << isize;
            qDebug() << "实际:" << actualSize;
            return false;
        }
        
        qDebug() << "✅ 压缩完整性验证通过";
        qDebug() << "压缩前:" << actualSize << "字节";
        qDebug() << "压缩后:" << gzipData.size() << "字节";
        qDebug() << "压缩比:" << QString::number((double)actualSize / gzipData.size(), 'f', 2);
        
        return true;
    }

    // 测试不同压缩级别
    static void testCompressionLevels(const QString& jsonData) {
        qDebug() << "\n=== 测试不同压缩级别 ===";
        QByteArray utf8Data = jsonData.toUtf8();

        for (int level = 1; level <= 9; level++) {
            QByteArray compressed = gzipCompressWithLevel(utf8Data, level);
            if (!compressed.isEmpty()) {
                qDebug() << QString("级别%1: %2字节 (压缩比: %3)")
                           .arg(level)
                           .arg(compressed.size())
                           .arg(QString::number((double)utf8Data.size() / compressed.size(), 'f', 2));
            }
        }
    }

    // 指定压缩级别的压缩
    static QByteArray gzipCompressWithLevel(const QByteArray& data, int level) {
        if (data.isEmpty()) return QByteArray();

        uLongf compressedSize = compressBound(data.size()) + 18;
        QByteArray compressed(compressedSize, 0);

        z_stream stream;
        stream.zalloc = Z_NULL;
        stream.zfree = Z_NULL;
        stream.opaque = Z_NULL;
        stream.avail_in = data.size();
        stream.next_in = (Bytef*)data.constData();
        stream.avail_out = compressed.size();
        stream.next_out = (Bytef*)compressed.data();

        int ret = deflateInit2(&stream, level, Z_DEFLATED, 15 + 16, 8, Z_DEFAULT_STRATEGY);
        if (ret != Z_OK) return QByteArray();

        ret = deflate(&stream, Z_FINISH);
        if (ret != Z_STREAM_END) {
            deflateEnd(&stream);
            return QByteArray();
        }

        uLongf actualSize = stream.total_out;
        deflateEnd(&stream);

        compressed.resize(actualSize);
        return compressed;
    }
};

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    // 你的JSON数据
    QString jsonData = R"({"LevelOrderList":[{"px":1,"UserID":5491031,"SerialNo":"11017649818871136055","IsPub":1,"ZoneServerID":"110104317255600","Title":"段位：暗部3阶-超影1阶 2S1A","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":10,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":100,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.00},{"px":2,"UserID":5376429,"SerialNo":"11017641628995604521","IsPub":1,"ZoneServerID":"110104317255600","Title":"【影1 52分-超影】 配置低来大佬 1s4a 战绩好超时不计","Price":16.0000,"Ensure1":15.0000,"Ensure2":15.0000,"Ensure":30.0000,"TimeLimit":24,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":47,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.39},{"px":3,"UserID":6779067,"SerialNo":"11017633663077532404","IsPub":1,"ZoneServerID":"110104117222100","Title":"百忍大战7冠扫码上号，安卓微信","Price":7.0000,"Ensure1":3.0000,"Ensure2":3.0000,"Ensure":6.0000,"TimeLimit":15,"Stamp":0,"Create":"新手USR201911020839","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":5,"UserID":5993179,"SerialNo":"11017649596870325924","IsPub":1,"ZoneServerID":"110104017182700","Title":"扫码 暗部1-影级1 3s 9a","Price":3.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":15,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":10.00},{"px":6,"UserID":23705152,"SerialNo":"11017648253291080683","IsPub":1,"ZoneServerID":"110104017182700","Title":"【指定永恒佐助一局35块】","Price":3.0000,"Ensure1":60.0000,"Ensure2":60.0000,"Ensure":120.0000,"TimeLimit":50,"Stamp":0,"Create":"新手USR2025080407066","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":""},{"px":7,"UserID":18205529,"SerialNo":"11017650091579216525","IsPub":1,"ZoneServerID":"110104317255600","Title":"上忍2阶-超影【14s30a】","Price":15.0000,"Ensure1":8.0000,"Ensure2":8.0000,"Ensure":16.0000,"TimeLimit":34,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":14.56},{"px":8,"UserID":5993179,"SerialNo":"11017649643981411737","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍1-影级1  2S 13A","Price":5.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":27,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":12.82},{"px":9,"UserID":5491031,"SerialNo":"11017649205725358022","IsPub":1,"ZoneServerID":"110104117222100","Title":"影级2阶-超影1 3s4a","Price":16.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":14,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":9,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":30.00},{"px":10,"UserID":23700204,"SerialNo":"11017650117379773935","IsPub":1,"ZoneServerID":"110104017182700","Title":"█别接破单了█一局35块,来打手█看要求都是高价","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2400,"Stamp":0,"Create":"新手USR2025080402197","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":11,"UserID":5993179,"SerialNo":"11017648446713332547","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 1s 8a","Price":25.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.77},{"px":12,"UserID":20332880,"SerialNo":"11018037735692841425","IsPub":1,"ZoneServerID":"110104017182700","Title":"巅峰前4","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":2,"Stamp":1992519617,"Create":"新手USR2024032405655","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":13,"UserID":13924284,"SerialNo":"11017649935881854223","IsPub":1,"ZoneServerID":"110104217241000","Title":"指定冰墩墩，套餐1打90天","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2160,"Stamp":0,"Create":"小喇叭发单","SameCity":"","SettleHour":1,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":14,"UserID":5376429,"SerialNo":"11017643783357962828","IsPub":1,"ZoneServerID":"110104017182700","Title":"【武当花火忍道分1622分到2250分】 角色名后是号主手机 联系号主扫码上号","Price":20.0000,"Ensure1":14.0000,"Ensure2":14.0000,"Ensure":28.0000,"TimeLimit":30,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":46,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":28.30},{"px":15,"UserID":5874699,"SerialNo":"11017613436873156575","IsPub":1,"ZoneServerID":"110104317255600","Title":"中忍2到超影（没实力别来，中途退，超时0结算）","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":3,"Stamp":0,"Create":"记得多喝热水。","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":13.64},{"px":16,"UserID":21716850,"SerialNo":"11017638838775995216","IsPub":1,"ZoneServerID":"110104217241000","Title":"苹果 影1到超 月初难打来个26选手 中途取消或接单不联系我扣钱","Price":14.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":5,"Stamp":0,"Create":"小河发单秒验收","SameCity":"","SettleHour":19,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.90},{"px":17,"UserID":23468051,"SerialNo":"11017644863363988536","IsPub":1,"ZoneServerID":"110104117222100","Title":"有需要打V团本的老板dd","Price":2.0000,"Ensure1":2.0000,"Ensure2":2.0000,"Ensure":4.0000,"TimeLimit":24,"Stamp":0,"Create":"新手USR2025071205302","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":18,"UserID":5993179,"SerialNo":"11017648444911673393","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3 36分-超影2500分 指定宇智波佐助 春野樱 漩涡鸣人","Price":66.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":80,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":46.81},{"px":19,"UserID":5993179,"SerialNo":"11017648444157533816","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3-超影  5s8a","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":52,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.00},{"px":20,"UserID":5993179,"SerialNo":"11017648883937125814","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 6s20a","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":18.56}],"RecordCount":29,"HasNew":0})";

    qDebug() << "=== 模拟服务器gzip压缩测试 ===";
    qDebug() << "服务器声明Content-Length: 3120字节";

    // 1. 模拟服务器压缩
    QByteArray serverCompressed = GzipCompressor::compressLikeServer(jsonData);

    if (serverCompressed.isEmpty()) {
        qDebug() << "❌ 压缩失败";
        return -1;
    }

    // 2. 对比结果
    qDebug() << "\n=== 对比结果 ===";
    qDebug() << "服务器声明:" << 3120 << "字节";
    qDebug() << "我们的压缩:" << serverCompressed.size() << "字节";
    qDebug() << "差异:" << (serverCompressed.size() - 3120) << "字节";

    if (abs(serverCompressed.size() - 3120) <= 100) {
        qDebug() << "✅ 压缩大小接近服务器声明 (差异在100字节内)";
    } else {
        qDebug() << "❌ 压缩大小与服务器声明差异较大";
    }

    // 3. 验证压缩完整性
    bool isValid = GzipCompressor::verifyCompression(serverCompressed, jsonData);

    // 4. 测试不同压缩级别
    GzipCompressor::testCompressionLevels(jsonData);

    // 5. 保存压缩文件用于对比
    QFile file("server_compressed.gz");
    if (file.open(QIODevice::WriteOnly)) {
        file.write(serverCompressed);
        file.close();
        qDebug() << "\n✅ 压缩文件已保存为 server_compressed.gz";
        qDebug() << "可以与你的在线压缩结果对比";
    }

    return 0;
}
