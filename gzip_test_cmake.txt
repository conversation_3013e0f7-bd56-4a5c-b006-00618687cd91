cmake_minimum_required(VERSION 3.16)
project(GzipTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 查找Qt6
find_package(Qt6 REQUIRED COMPONENTS Core)

# 查找zlib
find_package(ZLIB REQUIRED)

# 创建可执行文件
add_executable(gzip_test gzip_test.cpp)

# 链接库
target_link_libraries(gzip_test 
    Qt6::Core 
    ZLIB::ZLIB
)

# 设置Qt自动处理
set_target_properties(gzip_test PROPERTIES
    AUTOMOC ON
)
