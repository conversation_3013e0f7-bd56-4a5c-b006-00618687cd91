// 压缩功能使用示例
// 这个文件展示了如何在您的项目中使用压缩功能

#include <QApplication>
#include <QDebug>
#include "legacy/network/ultrafasttls.h"
#include "src/core/utils/logger.h"

class CompressionExample : public QObject
{
    Q_OBJECT

public:
    CompressionExample(QObject* parent = nullptr) : QObject(parent)
    {
        // 初始化UltraFastTLS - 压缩功能已自动启用
        m_tls = new UltraFastTLS(this);
        
        // 设置浏览器指纹（可选）
        m_tls->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
        
        // 启用详细日志以查看压缩过程（可选）
        m_tls->setQuietMode(false);
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "压缩示例初始化完成");
    }

    // 示例1: 请求支持压缩的API
    void requestCompressedApi()
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 示例1: 请求压缩API ===");
        
        // 这个API支持gzip压缩，程序会自动处理
        QString url = "https://api.github.com/users/octocat";
        QString response = m_tls->executeRequest(url);
        
        if (!response.isEmpty()) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, 
                QString("✅ 请求成功，响应长度: %1字符").arg(response.length()));
            
            // 解析JSON响应
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &error);
            if (error.error == QJsonParseError::NoError) {
                QJsonObject user = doc.object();
                NEW_LOG_INFO(NewLogCategory::NETWORK, 
                    QString("用户信息: %1 (%2)").arg(user["login"].toString()).arg(user["name"].toString()));
            }
        }
    }

    // 示例2: POST请求with压缩
    void postRequestWithCompression()
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 示例2: POST请求 ===");
        
        QString url = "https://httpbin.org/post";
        QString postData = "key1=value1&key2=value2&data=" + QString("x").repeated(1000); // 大数据测试压缩效果
        
        QString response = m_tls->executeRequest(url, postData);
        
        if (!response.isEmpty()) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, 
                QString("✅ POST请求成功，响应长度: %1字符").arg(response.length()));
        }
    }

    // 示例3: 批量请求（展示连接复用和压缩的组合效果）
    void batchRequestsWithCompression()
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 示例3: 批量请求 ===");
        
        QStringList urls = {
            "https://jsonplaceholder.typicode.com/posts/1",
            "https://jsonplaceholder.typicode.com/posts/2",
            "https://jsonplaceholder.typicode.com/posts/3"
        };
        
        for (const QString& url : urls) {
            QString response = m_tls->executeRequest(url);
            if (!response.isEmpty()) {
                QJsonParseError error;
                QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &error);
                if (error.error == QJsonParseError::NoError) {
                    QJsonObject post = doc.object();
                    NEW_LOG_INFO(NewLogCategory::NETWORK, 
                        QString("✅ 文章 %1: %2").arg(post["id"].toInt()).arg(post["title"].toString()));
                }
            }
        }
    }

    // 运行所有示例
    void runAllExamples()
    {
        requestCompressedApi();
        QThread::msleep(1000); // 等待1秒
        
        postRequestWithCompression();
        QThread::msleep(1000); // 等待1秒
        
        batchRequestsWithCompression();
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 所有示例完成 ===");
    }

private:
    UltraFastTLS* m_tls;
};

// 在您的实际代码中使用压缩功能的方法：

/*
// 方法1: 直接使用UltraFastTLS
UltraFastTLS* tls = new UltraFastTLS(this);
tls->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);

// 发起请求 - 压缩功能自动启用
QString response = tls->executeRequest("https://api.example.com/data");

// 方法2: 通过网络管理器使用
NetworkManager* manager = new NetworkManager(this);
auto adapter = std::make_unique<UltraFastTLSAdapter>();
manager->addEngine("ultrafasttls", std::move(adapter));

NetworkResult result = manager->executeRequest("https://api.example.com/data", "", QJsonObject());

// 方法3: 在现有的OrderAPI中使用（已经集成）
OrderAPI* api = new OrderAPI(this);
QString response = api->executeNetworkRequest("https://api.example.com/data", "", "");
*/

// 压缩功能的关键特性：
/*
1. 自动启用：无需额外配置，程序会自动请求和处理gzip压缩
2. 透明处理：您的代码无需改变，压缩/解压在底层自动完成
3. 性能优化：减少网络传输时间和带宽使用
4. 错误处理：如果解压失败，会自动回退到原始数据
5. 详细日志：可以启用详细日志查看压缩过程
6. 兼容性：支持标准的gzip格式，与所有主流服务器兼容
*/

#include "compression_example.moc"
