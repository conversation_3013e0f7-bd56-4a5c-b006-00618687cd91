#include <QCoreApplication>
#include <QDebug>
#include <QString>
#include <QByteArray>
#include "legacy/network/ultrafasttls.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 测试原始压缩数据获取 ===";
    
    // 创建UltraFastTLS实例
    UltraFastTLS tls;
    
    // 测试URL - 使用你之前测试的API
    QString testUrl = "你的测试API URL";  // 请替换为实际的URL
    
    qDebug() << "请求URL:" << testUrl;
    qDebug() << "注意: 程序已修改为返回原始压缩数据而不是解压后的JSON";
    qDebug() << "查看日志输出获取完整的HTTP头和压缩数据";
    
    // 执行请求
    QString response = tls.executeRequest(testUrl);
    
    qDebug() << "响应大小:" << response.length() << "字节";
    qDebug() << "响应类型: 原始压缩数据";
    
    // 显示响应的前100个字符（十六进制）
    QByteArray responseBytes = response.toLatin1();
    QString hexPreview;
    for (int i = 0; i < qMin(100, responseBytes.size()); i++) {
        hexPreview += QString("%1 ").arg((unsigned char)responseBytes[i], 2, 16, QChar('0'));
        if ((i + 1) % 16 == 0) hexPreview += "\n";
    }
    
    qDebug() << "响应数据预览(HEX):\n" << hexPreview;
    
    qDebug() << "\n=== 使用说明 ===";
    qDebug() << "1. 查看程序日志，找到 '=== 完整HTTP响应头 ===' 部分";
    qDebug() << "2. 复制所有的 'Header: xxx' 行";
    qDebug() << "3. 查找 '=== 完整压缩数据(HEX) ===' 部分";
    qDebug() << "4. 复制完整的十六进制数据";
    qDebug() << "5. 将这些数据提供给AI进行分析";
    
    return 0;
}
