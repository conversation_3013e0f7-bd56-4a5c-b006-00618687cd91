#include <QApplication>
#include <QDebug>
#include <QLoggingCategory>
#include <QTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include "legacy/network/ultrafasttls.h"
#include "src/core/utils/logger.h"

class CompressionTester : public QObject
{
    Q_OBJECT

public:
    CompressionTester(QObject* parent = nullptr) : QObject(parent)
    {
        // 初始化UltraFastTLS
        m_tls = new UltraFastTLS(this);
        m_tls->setBrowserFingerprint(UltraFastTLS::BrowserFingerprint::WECHAT_BROWSER);
        m_tls->setQuietMode(false); // 启用详细日志
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "压缩测试器初始化完成");
    }

public slots:
    void runTests()
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "开始压缩功能测试...");
        
        // 测试1: 请求支持gzip压缩的API
        testGzipCompression();
        
        // 测试2: 请求JSON API（通常会被压缩）
        testJsonApiCompression();
        
        // 测试3: 请求大文件（更容易看到压缩效果）
        testLargeContentCompression();
        
        // 5秒后退出程序
        QTimer::singleShot(5000, []() {
            NEW_LOG_INFO(NewLogCategory::NETWORK, "测试完成，程序即将退出");
            QApplication::quit();
        });
    }

private:
    void testGzipCompression()
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 测试1: 基本gzip压缩 ===");
        
        // 使用httpbin.org测试gzip压缩
        QString url = "https://httpbin.org/gzip";
        QString response = m_tls->executeRequest(url);
        
        if (!response.isEmpty()) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("✅ gzip测试成功，响应长度: %1字符").arg(response.length()));
            
            // 尝试解析JSON响应
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &error);
            if (error.error == QJsonParseError::NoError) {
                QJsonObject obj = doc.object();
                if (obj.contains("gzipped") && obj["gzipped"].toBool()) {
                    NEW_LOG_INFO(NewLogCategory::NETWORK, "✅ 服务器确认数据已被gzip压缩和解压");
                }
            }
        } else {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "❌ gzip测试失败");
        }
    }
    
    void testJsonApiCompression()
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 测试2: JSON API压缩 ===");
        
        // 测试一个返回JSON数据的API
        QString url = "https://api.github.com/repos/microsoft/vscode";
        QString response = m_tls->executeRequest(url);
        
        if (!response.isEmpty()) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("✅ JSON API测试成功，响应长度: %1字符").arg(response.length()));
            
            // 检查是否是有效的JSON
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &error);
            if (error.error == QJsonParseError::NoError) {
                QJsonObject obj = doc.object();
                if (obj.contains("name")) {
                    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("✅ JSON解析成功，项目名称: %1").arg(obj["name"].toString()));
                }
            }
        } else {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "❌ JSON API测试失败");
        }
    }
    
    void testLargeContentCompression()
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 测试3: 大内容压缩 ===");
        
        // 请求一个较大的JSON文件
        QString url = "https://jsonplaceholder.typicode.com/posts";
        QString response = m_tls->executeRequest(url);
        
        if (!response.isEmpty()) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("✅ 大内容测试成功，响应长度: %1字符").arg(response.length()));
            
            // 检查是否是有效的JSON数组
            QJsonParseError error;
            QJsonDocument doc = QJsonDocument::fromJson(response.toUtf8(), &error);
            if (error.error == QJsonParseError::NoError && doc.isArray()) {
                QJsonArray array = doc.array();
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("✅ JSON数组解析成功，包含 %1 个项目").arg(array.size()));
            }
        } else {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "❌ 大内容测试失败");
        }
    }

private:
    UltraFastTLS* m_tls;
};

// Qt消息处理器
void messageOutput(QtMsgType type, const QMessageLogContext &context, const QString &msg)
{
    Q_UNUSED(context);
    QString txt;
    switch (type) {
    case QtDebugMsg:
        txt = QString("Debug: %1").arg(msg);
        break;
    case QtWarningMsg:
        txt = QString("Warning: %1").arg(msg);
        break;
    case QtCriticalMsg:
        txt = QString("Critical: %1").arg(msg);
        break;
    case QtFatalMsg:
        txt = QString("Fatal: %1").arg(msg);
        break;
    case QtInfoMsg:
        txt = QString("Info: %1").arg(msg);
        break;
    }

    // 输出到控制台
    fprintf(stderr, "%s\n", txt.toLocal8Bit().constData());
    fflush(stderr);
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 安装消息处理器
    qInstallMessageHandler(messageOutput);
    
    NEW_LOG_INFO(NewLogCategory::SYSTEM, "压缩功能测试程序启动");
    
    CompressionTester tester;
    
    // 1秒后开始测试
    QTimer::singleShot(1000, &tester, &CompressionTester::runTests);
    
    return app.exec();
}

#include "test_compression.moc"
