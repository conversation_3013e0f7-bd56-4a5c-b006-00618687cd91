cmake_minimum_required(VERSION 3.21)

project(CompressionTest LANGUAGES CXX)

# 使用与主项目相同的配置
set(VCPKG_ROOT "C:/Users/<USER>/Desktop/vcpkg-master/vcpkg-master")
set(CMAKE_TOOLCHAIN_FILE "${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake" CACHE STRING "Vcpkg toolchain file")
set(VCPKG_TARGET_TRIPLET "x64-windows" CACHE STRING "Vcpkg target triplet")

# 查找Qt
find_package(Qt6 REQUIRED COMPONENTS Widgets Network Concurrent)

# 查找OpenSSL
set(OPENSSL_SEARCH_PATHS
    "C:/Program Files/OpenSSL-Win64"
    "C:/Users/<USER>/Desktop/vcpkg-master/vcpkg-master/installed/x64-windows"
    "C:/OpenSSL-Win64"
    "C:/vcpkg/installed/x64-windows"
    "C:/Libraries/OpenSSL"
)

find_package(OpenSSL QUIET)

if(NOT OpenSSL_FOUND)
    foreach(SEARCH_PATH ${OPENSSL_SEARCH_PATHS})
        if(EXISTS "${SEARCH_PATH}/include/openssl/ssl.h")
            set(OPENSSL_ROOT_DIR "${SEARCH_PATH}")
            set(OPENSSL_INCLUDE_DIR "${SEARCH_PATH}/include")

            find_library(OPENSSL_SSL_LIBRARY
                NAMES libssl ssl ssleay32 ssl-3-x64 libssl-3-x64
                PATHS
                    "${SEARCH_PATH}/lib/VC/x64/MD"
                    "${SEARCH_PATH}/lib/VC/x64/MT"
                    "${SEARCH_PATH}/lib/VC/x64"
                    "${SEARCH_PATH}/lib/x64"
                    "${SEARCH_PATH}/lib"
                NO_DEFAULT_PATH)
            find_library(OPENSSL_CRYPTO_LIBRARY
                NAMES libcrypto crypto libeay32 crypto-3-x64 libcrypto-3-x64
                PATHS
                    "${SEARCH_PATH}/lib/VC/x64/MD"
                    "${SEARCH_PATH}/lib/VC/x64/MT"
                    "${SEARCH_PATH}/lib/VC/x64"
                    "${SEARCH_PATH}/lib/x64"
                    "${SEARCH_PATH}/lib"
                NO_DEFAULT_PATH)

            if(OPENSSL_SSL_LIBRARY AND OPENSSL_CRYPTO_LIBRARY)
                set(OPENSSL_LIBRARIES ${OPENSSL_SSL_LIBRARY} ${OPENSSL_CRYPTO_LIBRARY})
                set(OpenSSL_FOUND TRUE)
                message(STATUS "✅ OpenSSL found at: ${SEARCH_PATH}")
                break()
            endif()
        endif()
    endforeach()
endif()

# 查找zlib
find_package(ZLIB QUIET)

if(NOT ZLIB_FOUND)
    if(EXISTS "${VCPKG_ROOT}/installed/x64-windows/include/zlib.h")
        set(ZLIB_INCLUDE_DIRS "${VCPKG_ROOT}/installed/x64-windows/include")
        find_library(ZLIB_LIBRARIES
            NAMES z zlib zlibstatic
            PATHS "${VCPKG_ROOT}/installed/x64-windows/lib"
            NO_DEFAULT_PATH)
        if(ZLIB_LIBRARIES)
            set(ZLIB_FOUND TRUE)
            message(STATUS "✅ 找到vcpkg zlib: ${ZLIB_LIBRARIES}")
        endif()
    endif()

    if(NOT ZLIB_FOUND AND EXISTS "C:/Libraries/zlib131/zlib-1.3.1")
        add_subdirectory("C:/Libraries/zlib131/zlib-1.3.1" ${CMAKE_BINARY_DIR}/zlib)
        set(ZLIB_FOUND TRUE)
        set(ZLIB_TARGET "zlibstatic")
        message(STATUS "✅ 使用本地zlib源码")
    endif()
endif()

if(ZLIB_FOUND)
    add_definitions(-DHAS_ZLIB)
else()
    add_definitions(-DNO_ZLIB)
endif()

if(OpenSSL_FOUND)
    add_definitions(-DOPENSSL_FOUND)
endif()

# 设置Qt标准项目
qt_standard_project_setup()

# 创建可执行文件
qt_add_executable(CompressionTest
    test_compression.cpp
    # 需要的源文件
    legacy/network/ultrafasttls.h
    legacy/network/ultrafasttls.cpp
    src/core/utils/logger.h
    src/core/utils/logger.cpp
)

# 设置C++17
target_compile_features(CompressionTest PRIVATE cxx_std_17)

# 包含目录
target_include_directories(CompressionTest PRIVATE
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core/utils
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy
    ${CMAKE_CURRENT_SOURCE_DIR}/legacy/network
)

# 链接库
set(LINK_LIBRARIES
    Qt6::Widgets
    Qt6::Network
    Qt6::Concurrent
    ws2_32
    crypt32
)

# 添加zlib库
if(ZLIB_FOUND)
    if(DEFINED ZLIB_TARGET)
        list(APPEND LINK_LIBRARIES ${ZLIB_TARGET})
    else()
        list(APPEND LINK_LIBRARIES ${ZLIB_LIBRARIES})
        target_include_directories(CompressionTest PRIVATE ${ZLIB_INCLUDE_DIRS})
    endif()
endif()

# 添加OpenSSL库
if(OpenSSL_FOUND)
    list(APPEND LINK_LIBRARIES ${OPENSSL_LIBRARIES})
    target_include_directories(CompressionTest PRIVATE ${OPENSSL_INCLUDE_DIR})
else()
    list(APPEND LINK_LIBRARIES secur32 schannel)
endif()

target_link_libraries(CompressionTest PRIVATE ${LINK_LIBRARIES})

message(STATUS "🔗 链接库列表: ${LINK_LIBRARIES}")
