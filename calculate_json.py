#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 计算你提供的JSON明文大小

json_data = '''{"LevelOrderList":[{"px":1,"UserID":23700204,"SerialNo":"11017650117379773935","IsPub":1,"ZoneServerID":"110104017182700","Title":"█别接破单了█一局35块,来打手█看要求都是高价","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2400,"Stamp":0,"Create":"新手USR2025080402197","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":""},{"px":2,"UserID":11353372,"SerialNo":"11017602067088687893","IsPub":1,"ZoneServerID":"110104317255600","Title":"AC、 百忍全奖励","Price":20.0000,"Ensure1":10.0000,"Ensure2":10.0000,"Ensure":20.0000,"TimeLimit":12,"Stamp":0,"Create":"火影蔡徐坤","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":3,"UserID":21716850,"SerialNo":"11017638838775995216","IsPub":1,"ZoneServerID":"110104217241000","Title":"苹果 影1到超 月初难打来个26选手 中途取消或接单不联系我扣钱","Price":14.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":5,"Stamp":0,"Create":"小河发单秒验收","SameCity":"","SettleHour":19,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.90},{"px":4,"UserID":5993179,"SerialNo":"11017648444157533816","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3-超影  5s8a","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":52,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.00},{"px":5,"UserID":5993179,"SerialNo":"11017648446713332547","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 1s 8a","Price":25.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.77},{"px":6,"UserID":23468051,"SerialNo":"11017644863363988536","IsPub":1,"ZoneServerID":"110104117222100","Title":"有需要打V团本的老板dd","Price":2.0000,"Ensure1":2.0000,"Ensure2":2.0000,"Ensure":4.0000,"TimeLimit":24,"Stamp":0,"Create":"新手USR2025071205302","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":7,"UserID":5993179,"SerialNo":"11017649643981411737","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍1-影级1  2S 13A","Price":5.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":27,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":12.82},{"px":8,"UserID":6779067,"SerialNo":"11017633663077532404","IsPub":1,"ZoneServerID":"110104117222100","Title":"百忍大战7冠扫码上号，安卓微信","Price":7.0000,"Ensure1":3.0000,"Ensure2":3.0000,"Ensure":6.0000,"TimeLimit":15,"Stamp":0,"Create":"新手USR201911020839","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":9,"UserID":7032583,"SerialNo":"11017649234449896060","IsPub":1,"ZoneServerID":"110104317255600","Title":"一个月日常加周胜，秘境探险跟团本，每天日常能搞的都要搞","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":2400,"Stamp":0,"Create":"ε?(?> ? <)?","SameCity":"","SettleHour":48,"SitePrice":"","Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":11,"UserID":5376429,"SerialNo":"11017641628995604521","IsPub":1,"ZoneServerID":"110104317255600","Title":"【影1 52分-超影】 配置低来大佬 1s4a 战绩好超时不计","Price":16.0000,"Ensure1":15.0000,"Ensure2":15.0000,"Ensure":30.0000,"TimeLimit":24,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":47,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.39},{"px":12,"UserID":18205529,"SerialNo":"11017650091579216525","IsPub":1,"ZoneServerID":"110104317255600","Title":"上忍2阶-超影【14s30a】","Price":15.0000,"Ensure1":8.0000,"Ensure2":8.0000,"Ensure":16.0000,"TimeLimit":34,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":14.56},{"px":14,"UserID":5376429,"SerialNo":"11017643783357962828","IsPub":1,"ZoneServerID":"110104017182700","Title":"【武当花火忍道分1622分到2250分】 角色名后是号主手机 联系号主扫码上号","Price":20.0000,"Ensure1":14.0000,"Ensure2":14.0000,"Ensure":28.0000,"TimeLimit":30,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":46,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":28.30},{"px":15,"UserID":22049761,"SerialNo":"11018267638501169537","IsPub":1,"ZoneServerID":"110104017182700","Title":"更多玩法全部宝箱＋超影","Price":2.0000,"Ensure1":25.0000,"Ensure2":25.0000,"Ensure":50.0000,"TimeLimit":2,"Stamp":2097547561,"Create":"新手USR2025011103301","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":16,"UserID":18205529,"SerialNo":"11017650162378137164","IsPub":1,"ZoneServerID":"110104017182700","Title":"★ 暗部3阶-超影1】3s10a/接单电话通知","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":50,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.50},{"px":17,"UserID":5993179,"SerialNo":"11017648883937125814","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 6s20a","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":18.56},{"px":18,"UserID":5874699,"SerialNo":"11017613436873156575","IsPub":1,"ZoneServerID":"110104317255600","Title":"中忍2到超影（没实力别来，中途退，超时0结算）","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":3,"Stamp":0,"Create":"记得多喝热水。","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":13.64},{"px":19,"UserID":13924284,"SerialNo":"11017649935881854223","IsPub":1,"ZoneServerID":"110104217241000","Title":"指定冰墩墩，套餐1打90天","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2160,"Stamp":0,"Create":"小喇叭发单","SameCity":"","SettleHour":1,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":20,"UserID":5993179,"SerialNo":"11017649596870325924","IsPub":1,"ZoneServerID":"110104017182700","Title":"扫码 暗部1-影级1 3s 9a","Price":3.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":15,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":10.00}],"RecordCount":27,"HasNew":0}'''

print("=== JSON明文大小计算 ===")

# 计算UTF-8字节数
utf8_bytes = json_data.encode('utf-8')
print(f"JSON字符数: {len(json_data)}")
print(f"UTF-8字节数: {len(utf8_bytes)}")

# 字符分析
ascii_count = 0
chinese_count = 0
special_count = 0

for char in json_data:
    code = ord(char)
    if code < 128:
        ascii_count += 1
    elif 0x4E00 <= code <= 0x9FFF:  # 中文字符范围
        chinese_count += 1
    else:
        special_count += 1

print(f"\n=== 字符分析 ===")
print(f"ASCII字符: {ascii_count}个")
print(f"中文字符: {chinese_count}个")
print(f"特殊字符: {special_count}个")

# 预测字节数验证
predicted_bytes = ascii_count + chinese_count * 3 + special_count * 2
print(f"预测UTF-8字节数: {predicted_bytes}")
print(f"实际UTF-8字节数: {len(utf8_bytes)}")
print(f"预测准确性: {'✅' if predicted_bytes == len(utf8_bytes) else '❌'}")

# 特殊字符详情
special_chars = set()
for char in json_data:
    if ord(char) >= 128:
        special_chars.add(char)

print(f"\n=== 特殊字符详情 ===")
print(f"发现的特殊字符: {sorted(special_chars)}")

# 计算每个特殊字符的字节数
for char in sorted(special_chars):
    char_bytes = char.encode('utf-8')
    print(f"'{char}' (U+{ord(char):04X}): {len(char_bytes)}字节")

print(f"\n=== 最终结果 ===")
print(f"📏 UTF-8字节数: {len(utf8_bytes)} 字节")
print(f"📝 字符数: {len(json_data)} 字符")
print(f"📊 字节/字符比: {len(utf8_bytes)/len(json_data):.2f}")

# 模拟gzip压缩
import gzip

print(f"\n=== gzip压缩测试 ===")
print(f"服务器声明Content-Length: 3154字节 (gzip压缩后)")
print(f"响应头确认: Content-Encoding: gzip")

# 测试不同压缩级别
for level in range(1, 10):
    compressed = gzip.compress(utf8_bytes, compresslevel=level)
    print(f"级别{level}: {len(compressed)}字节 (差异: {len(compressed) - 3154:+d})")

# 找最接近的级别
best_level = None
min_diff = float('inf')

for level in range(1, 10):
    compressed = gzip.compress(utf8_bytes, compresslevel=level)
    diff = abs(len(compressed) - 3154)
    if diff < min_diff:
        min_diff = diff
        best_level = level

print(f"\n=== 最佳匹配 ===")
best_compressed = gzip.compress(utf8_bytes, compresslevel=best_level)
print(f"最接近级别: {best_level}")
print(f"压缩后大小: {len(best_compressed)}字节")
print(f"服务器声明: 3154字节")
print(f"差异: {len(best_compressed) - 3154:+d}字节")

if abs(len(best_compressed) - 3154) <= 50:
    print("✅ 非常接近！差异在50字节内")
elif abs(len(best_compressed) - 3154) <= 100:
    print("✅ 比较接近！差异在100字节内")
else:
    print("❌ 差异较大")

# 验证ISIZE
import struct
isize = struct.unpack('<I', best_compressed[-4:])[0]
print(f"\n=== ISIZE验证 ===")
print(f"gzip ISIZE: {isize}字节")
print(f"实际UTF-8: {len(utf8_bytes)}字节")
print(f"ISIZE匹配: {'✅' if isize == len(utf8_bytes) else '❌'}")

# 保存压缩文件
with open('test_compressed.gz', 'wb') as f:
    f.write(best_compressed)
print(f"\n✅ 压缩文件已保存为 test_compressed.gz")
print(f"可以与服务器的gzip数据对比")
