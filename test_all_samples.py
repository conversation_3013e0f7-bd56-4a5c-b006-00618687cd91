#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip

# 所有样本数据
samples = [
    {
        "name": "样本1",
        "content_length": 3154,
        "records": 27,
        "json": '''{"LevelOrderList":[{"px":1,"UserID":23700204,"SerialNo":"11017650117379773935","IsPub":1,"ZoneServerID":"110104017182700","Title":"█别接破单了█一局35块,来打手█看要求都是高价","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2400,"Stamp":0,"Create":"新手USR2025080402197","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":""}],"RecordCount":27,"HasNew":0}'''
    },
    {
        "name": "样本2", 
        "content_length": 3177,
        "records": 31,
        "json": '''{"LevelOrderList":[{"px":1,"UserID":5491031,"SerialNo":"11017649818871136055","IsPub":1,"ZoneServerID":"110104317255600","Title":"段位：暗部3阶-超影1阶 2S1A","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":10,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":100,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.00}],"RecordCount":31,"HasNew":0}'''
    },
    {
        "name": "样本3",
        "content_length": 2960,
        "records": 32,
        "json": '''{"LevelOrderList":[{"px":1,"UserID":13924284,"SerialNo":"11017649935881854223","IsPub":1,"ZoneServerID":"110104217241000","Title":"指定冰墩墩，套餐1打90天","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2160,"Stamp":0,"Create":"小喇叭发单","SameCity":"","SettleHour":1,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00}],"RecordCount":32,"HasNew":0}'''
    },
    {
        "name": "样本4",
        "content_length": 3186,
        "records": 33,
        "json": '''{"LevelOrderList":[{"px":1,"UserID":5376429,"SerialNo":"11017643783357962828","IsPub":1,"ZoneServerID":"110104017182700","Title":"【武当花火忍道分1622分到2250分】 角色名后是号主手机 联系号主扫码上号","Price":20.0000,"Ensure1":14.0000,"Ensure2":14.0000,"Ensure":28.0000,"TimeLimit":30,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":46,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":28.30}],"RecordCount":33,"HasNew":0}'''
    },
    {
        "name": "样本5",
        "content_length": 2932,
        "records": 32,
        "json": '''{"LevelOrderList":[{"px":1,"UserID":5993179,"SerialNo":"11017648883937125814","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 6s20a","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":18.56}],"RecordCount":32,"HasNew":0}'''
    }
]

print("=== 实际压缩测试所有样本 ===")

total_samples = len(samples)
accurate_predictions = 0

for sample in samples:
    print(f"\n--- {sample['name']} ---")
    
    # 基础数据
    json_str = sample['json']
    utf8_data = json_str.encode('utf-8')
    server_cl = sample['content_length']
    records = sample['records']
    
    print(f"RecordCount: {records}")
    print(f"JSON UTF-8大小: {len(utf8_data)}字节")
    print(f"服务器Content-Length: {server_cl}字节")
    
    # 测试不同压缩级别
    best_match = None
    best_diff = float('inf')
    
    for level in range(0, 10):
        compressed = gzip.compress(utf8_data, compresslevel=level)
        diff = abs(len(compressed) - server_cl)
        
        if diff < best_diff:
            best_diff = diff
            best_match = (level, len(compressed))
        
        print(f"  级别{level}: {len(compressed)}字节 (差异:{diff})")
    
    # 分析最佳匹配
    best_level, best_size = best_match
    overhead = server_cl - best_size
    
    print(f"最佳匹配: 级别{best_level}, {best_size}字节")
    print(f"包装开销: {overhead}字节")
    
    # 验证解压
    try:
        test_compressed = gzip.compress(utf8_data, compresslevel=best_level)
        decompressed = gzip.decompress(test_compressed)
        if decompressed.decode('utf-8') == json_str:
            print("✅ 压缩/解压验证成功")
        else:
            print("❌ 压缩/解压验证失败")
    except Exception as e:
        print(f"❌ 解压失败: {e}")
    
    # 检查是否接近完美匹配
    if best_diff < 50:  # 差异小于50字节认为是接近匹配
        accurate_predictions += 1
        print(f"✅ 接近匹配 (差异{best_diff}字节)")
    else:
        print(f"❌ 差异较大 (差异{best_diff}字节)")

print(f"\n=== 总体分析 ===")
print(f"总样本数: {total_samples}")
print(f"接近匹配: {accurate_predictions}")
print(f"匹配率: {accurate_predictions/total_samples*100:.1f}%")

# 分析包装开销模式
print(f"\n=== 包装开销分析 ===")
overheads = []
for sample in samples:
    json_str = sample['json']
    utf8_data = json_str.encode('utf-8')
    compressed = gzip.compress(utf8_data, compresslevel=6)  # 标准级别
    overhead = sample['content_length'] - len(compressed)
    overheads.append(overhead)
    print(f"{sample['name']}: {overhead}字节开销")

avg_overhead = sum(overheads) / len(overheads)
min_overhead = min(overheads)
max_overhead = max(overheads)

print(f"\n包装开销统计:")
print(f"平均开销: {avg_overhead:.1f}字节")
print(f"最小开销: {min_overhead}字节")
print(f"最大开销: {max_overhead}字节")
print(f"开销范围: {max_overhead - min_overhead}字节")

if max_overhead - min_overhead < 100:
    print("✅ 开销相对稳定，可能确实有固定的包装")
else:
    print("❌ 开销变化较大，可能不是固定包装")

print(f"\n🎯 结论:")
print(f"如果大部分样本都接近匹配，说明服务器就是标准gzip + 少量开销")
print(f"如果差异很大，说明我之前的复杂理论可能是对的")
