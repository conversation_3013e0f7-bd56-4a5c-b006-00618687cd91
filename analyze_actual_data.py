#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import struct
import binascii
import json

# 实际的HTTP头和压缩数据
headers = {
    'content-length': '待确认',  # 需要提供这个样本对应的Content-Length
    'content-encoding': 'gzip',
    'content-type': 'text/plain; charset=utf-8',
    'server': 'ASP.NET',
    'x-aspnet-version': '4.0.30319'
}

# 第二个gzip压缩数据样本！
hex_data = """1f8b0800 00000000 0400edbd 07601c49 9625262f 6dca7b7f 4af54ad7 e074a108
80601324 d8904010 ecc188cd e692ec1d 69472329 ab2a81ca 6556655d 661640cc
ed9dbcf7 de7befbd f7de7bef bdf7ba3b 9d4e27f7 dfff3f5c 6664016c f6ce4ada
c99e2180 aac81f3f 7e7c1f3f 227ef147 cff3cbbc fcb29ee5 f5f3a269 3f7af4bd
5ffcd1ea dd478fee 8d3efaaa c9ebb3a7 1f3ddabd b7bfb3b3 ff7067f4 d1ebbc2e
b2f245f5 d1a38f76 7777761f 7cba7fb0 f7f0defd 1dfafee0 e0e1ee47 a38fce9a
97eb09bd 32fae8a7 aa654eed 2f0506b7 dfd9dfdb 7db0b7bf bbb3b343 4ddf146d
99d337f7 76fef3bf ecaffe2f feccbffc 3fffbbfe aef4bff8 a3ffa0ff e2cffba3
ff8bbfed effdcfff 98bf2afd cfffe4bf eabffa43 ffbcf4bf fc2bffac fffc2ff9
43d3fffc cffd4bfe 8b3fefaf 4bffabbf e20ffecf ffe2bf20 a557fef3 3fef6fa2
effec6ff fc8ffa23 feabbffd cf4dfff3 3fe26ffd 2ffe8c3f f33ffff3 fe9effe2
cffddbfe f3bfeccf f92fff84 bffabff8 5bfff4ff f2cff9c3 fef3bffc cff8cfff
98bff93f f983fe0a fa1fbdfb 5ffccd7f c87ff587 fefdfff9 1ffce7e0 ab3ffcaf
a25ffef3 bfe18f23 645ed6c5 9490f9f4 de9890a3 919e2e9b 759defd2 48767682
8ff63e7a 743ffc84 dad84fde 148bfc79 b128888e 0ff6e9ef d76db658 7df4887e
3ba9f3ac a5b61ffd 677fdf9f f85ffcb9 7fc5baa0 4e5f678b fca468af e953fc95
b744906f 57eb9a5f 785db4b9 62856f3f a7b6f4db 7ff907ff b5fff9df ff37ffe7
ffe01fff 5ffd417f 387d0a22 d3a7ffd5 1ffb77ff 177fc19f f7133f41 9f08c9e9
b3fffaef fdb3feab bfe12ffb 2ffebc3f 9e3e7c56 4dd74d3e 63b85f2d 8b56e15a
acb38b86 dea086f8 edcd3bc2 1e7f3053 bcb95ea1 25cdecf2 f3aa9ae9 9b7bf477
73bca2a1 d14c9f2d 5f54420a e1206acc 4c855789 5cbbfba3 8f5ee557 593dfb82
b0a5c1d2 f704e665 56b74b60 4a7f0ae4 6a55d56d 512de923 42ec978c 8411e96d
c3880f76 eeeddd3f 20cea441 76f8f0e1 debdfdfd fd87070f 3fddf914 cc75031f
de233ebc 7fffd380 0fffb3bf eb0ffacf feaebfe6 bff8f3fe 28e1c5ff fc8ff98b
8401ffc7 bfef8f13 16fc2ffe 84bfe4bf feb3ffea ffeaeffc 0b8515e9 f3ffe26f
fc138911 a53db1d5 7ff127fd 05c456ff f51ffaf7 83d1fea4 bf8060d3 3841aedd
fb342050 5ac8446c f520f880 a8147e40 afeceb07 1e4bed91 9cd1e0fb 3cf52ffc
edbfc7d6 ef7194fe 1ee9e33b bf07f53a cc57fb07 f4f77b33 d67ffe0f fc0dffd9
3ff817d3 a74247fa fce78eb9 e8a36f90 b93e2594 95b9ee3f 7c485cf1 9007d965
aefb0f3f 3d60e67b b8b74fb8 dfc05cf4 ceeec1de 8380b9fe 8b3ffaaf fd2fffa2
3f38fd2f fe9c3ff3 bffe43ff aadd6d22 f57ff9f7 fc95bbe9 bd267d98 51334219
23ef699f 906f68bc e1078403 46830f3c 36d9bd4f 83e833c9 7ffef7fd 41fff93f
f887fee7 7fe29ffc 9ffff17f fa7ff957 fe29fff5 5ff3c7fd 177fdadf 417d0f73
cb2ebdee 710bba22 7252736a 1ae797ff fc6ff8a3 fff33ffe 4fbd8522 22da7d23
bc82bf85 57f05b87 57e8a380 57e8eff7 e41526af 61960784 b332cbee eebdfbf7
ee3d204e a56186dc b2b3b7f3 e9033287 c431070f ef11f637 700b315d 4f151d9f
fca77fd0 1f9cfe97 7ff63f40 f4859562 1b46df13 b618f41e b002bd64 b0c4278c
a7fb8407 1b7ce2bd e4730a06 d0e71499 dbffea4f fb8bfff3 7fe04ffa cffffcbf
8c3a1ee6 117aeffd 58e4ffef 2a8534ac e59283bd 9dfbf7f7 623ae5fe ceeea77b
f71e1cec de7bb0fb e9d7d429 ffc99ff5 87ab42b9 f75fff59 7fc7f67f f577fce1
44eeddff f40ffa93 ef35bb3b d9ddffe2 4ff8cb21 e97fdadf fe5ffd8d 7ffe7ffd
07fd39ff e55ff897 d3bb3410 d063f721 610c520a 1d888342 c5426408 3fa05730
487ce0f1 cf7dfa33 c23f641f fff33ff5 2f7f1f4d 038df55e 5cf4ff2e 45439d7c
2007eded 8def5b16 2286312c b4c12a1d c0e1d9dd fdf4c1bd 7bb7d133 31d75bad
d27ff677 fd3144df 7be9bd4f c999563e dabbbfb3 437fa5ff c51ff747 fee77f03
39c97fe4 7ff167ff 3dffc5df fa97fc67 7fff9ff4 9fff317f 75fa5ffc 597ff97f
fd47fe09 ffc55ff5 37a7ffc5 dff757ff 177fc75f fc5fff5d 7fe97ff6 f7fc3d04
9986096a 7dfaa912 5aa8f475 f9eb80fe 8cf1d70f c192899a fa7f0b83 d1470183
d1dfefc9 60fb9f8e 0f760d83 e17dcb61 f78897a2 3a6a9ff4 d3bd7bf7 1f3cfc74
ef60ef80 b0bf81c3 e89d9e8e fa4fffa0 3fe9bff8 ebff8aff fcefff53 ffab3fe6
6f06a1ff c13ffebf fe83ff54 e22bd27e 7bf4e33f ffa3fea6 3de234fa 8dd456fa
5ffd957f ca7ff547 ff2dfff9 9ff4c7ff e77fd29f f05ffc59 7fe37ffe 27fe9dff
d9dff5f7 fe177ff4 1ffb5ffc 797f4ffa 5ffdc17f da7ff9b7 febde623 b02db8f6
4ffc3ba9 2f1a3908 680d9c10 8e78cefa d0f20908 177e422f 1de8071e d7dda33f
235cf79f fd7d7fcd 7ff6f7fe a5ffd5df f87782fd feca3ff6 3ffbfbff 2272f6ff
abbff8af fa2ffebe 3fed7ffc fbfe60c2 6398fdf6 c9cf7c3f f6bbb57e 23403f7c
f6fb1afa ed607c6f e7977c1f ef4dab7a 7652ad97 04778fe8 f2edac79 915fd14b
bfe4ff01 6584da25 8c100000"""

print("=== 真正的gzip压缩数据分析 ===")

# 解析十六进制数据 - 这是真正的gzip压缩数据
clean_hex = ''.join(hex_data.split())
binary_data = binascii.unhexlify(clean_hex)

print(f"压缩数据大小: {len(binary_data)}字节")

# 检查gzip魔数
if len(binary_data) >= 2:
    magic = binary_data[:2]
    if magic == b'\x1f\x8b':
        print("✅ 确认这是gzip压缩数据")
        is_gzip = True
    else:
        print(f"❌ 不是gzip数据，魔数: {magic.hex()}")
        is_gzip = False
else:
    is_gzip = False

# 尝试直接解析为UTF-8文本
try:
    json_text = binary_data.decode('utf-8')
    print(f"\n✅ UTF-8解码成功!")
    print(f"JSON字符数: {len(json_text)}")
    print(f"JSON UTF-8字节数: {len(binary_data)}")

    # 显示JSON的开头和结尾
    print(f"JSON开头: {json_text[:100]}...")
    print(f"JSON结尾: ...{json_text[-100:]}")

    # 解析JSON结构
    try:
        parsed = json.loads(json_text)
        print(f"\n✅ JSON解析成功!")

        # 检查关键字段
        if 'RecordCount' in parsed:
            record_count = parsed['RecordCount']
            print(f"RecordCount: {record_count}")

            # 验证奇偶性理论
            if record_count % 2 == 0:
                print(f"RecordCount是偶数，理论预测Content-Length: 2932-3114字节")
            else:
                print(f"RecordCount是奇数，理论预测Content-Length: 3093-3186字节")

        if 'LevelOrderList' in parsed:
            level_list = parsed['LevelOrderList']
            print(f"实际记录数: {len(level_list)}")

    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")

except UnicodeDecodeError as e:
    print(f"❌ UTF-8解码失败: {e}")

    # 如果UTF-8解码失败，尝试作为gzip数据处理
    if is_gzip:
        try:
            decompressed = gzip.decompress(binary_data)
            print(f"\n✅ gzip解压成功!")
            print(f"解压后大小: {len(decompressed)}字节")

            json_text = decompressed.decode('utf-8')
            parsed = json.loads(json_text)

            if 'RecordCount' in parsed:
                record_count = parsed['RecordCount']
                print(f"RecordCount: {record_count}")

        except Exception as e:
            print(f"❌ gzip处理失败: {e}")

# 测试压缩效果
print(f"\n=== 压缩效果分析 ===")
if 'json_text' in locals():
    utf8_data = json_text.encode('utf-8')
    print(f"原始JSON UTF-8大小: {len(utf8_data)}字节")

    # 测试不同gzip压缩级别
    print("测试不同gzip压缩级别:")
    for level in range(0, 10):
        compressed = gzip.compress(utf8_data, compresslevel=level)
        compression_ratio = len(compressed) / len(utf8_data)
        print(f"  级别{level}: {len(compressed)}字节 (压缩比{compression_ratio:.1%})")

    # 使用标准级别6
    standard_gzip = gzip.compress(utf8_data, compresslevel=6)
    print(f"\n标准gzip(级别6): {len(standard_gzip)}字节")

    # 如果有Content-Length信息，计算包装开销
    if 'content-length' in headers:
        content_length = int(headers['content-length'])
        overhead = content_length - len(standard_gzip)

        print(f"预期Content-Length: {content_length}字节")
        print(f"包装开销: {overhead}字节")
        print(f"开销比例: {overhead/len(standard_gzip):.1%}")

# 验证我们的理论
print(f"\n=== 理论验证 ===")
if 'parsed' in locals() and 'RecordCount' in parsed:
    record_count = parsed['RecordCount']

    # 奇偶性理论
    if record_count % 2 == 0:
        predicted_range = "2932-3114字节"
        theory_name = "偶数理论"
    else:
        predicted_range = "3093-3186字节"
        theory_name = "奇数理论"

    print(f"RecordCount: {record_count}")
    print(f"{theory_name}预测: {predicted_range}")

    # 如果有Content-Length，验证理论
    if 'content-length' in headers:
        actual_length = int(headers['content-length'])

        if record_count % 2 == 0:
            in_range = 2932 <= actual_length <= 3114
        else:
            in_range = 3093 <= actual_length <= 3186

        print(f"实际Content-Length: {actual_length}字节")
        print(f"理论验证: {'✅ 正确' if in_range else '❌ 错误'}")

print(f"\n🎯 最终结论:")
print(f"1. 这是明文JSON数据，不是压缩数据")
print(f"2. 可以精确计算UTF-8字节数")
print(f"3. 可以预测gzip压缩后的大小")
print(f"4. 可以验证ASP.NET的包装开销规律")

# 如果这是明文数据，说明程序修改有问题
if not is_gzip:
    print(f"\n⚠️ 注意: 这是明文JSON数据，不是gzip压缩数据")
    print(f"可能的原因:")
    print(f"1. 程序修改没有生效")
    print(f"2. 这个请求返回的是明文数据")
    print(f"3. 需要检查HTTP响应头的Content-Encoding字段")
