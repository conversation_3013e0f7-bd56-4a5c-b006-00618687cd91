#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟服务器gzip压缩验证程序
"""

import gzip
import struct
import json

def compress_like_server(json_data, compression_level=6):
    """模拟服务器的gzip压缩"""
    print(f"=== 模拟服务器gzip压缩 (级别{compression_level}) ===")
    
    # 1. 转换为UTF-8字节（模拟服务器行为）
    utf8_bytes = json_data.encode('utf-8')
    print(f"原始JSON UTF-8大小: {len(utf8_bytes)} 字节")
    
    # 2. 使用gzip压缩
    compressed = gzip.compress(utf8_bytes, compresslevel=compression_level)
    print(f"压缩后大小: {len(compressed)} 字节")
    
    # 3. 验证ISIZE字段
    isize = extract_isize(compressed)
    print(f"ISIZE字段: {isize} 字节")
    print(f"ISIZE匹配: {'✅' if isize == len(utf8_bytes) else '❌'}")
    
    return compressed

def extract_isize(gzip_data):
    """提取gzip的ISIZE字段"""
    if len(gzip_data) < 4:
        return 0
    
    # ISIZE是最后4个字节，小端序
    isize_bytes = gzip_data[-4:]
    isize = struct.unpack('<I', isize_bytes)[0]  # 小端序无符号整数
    return isize

def verify_compression(gzip_data, original_json):
    """验证压缩完整性"""
    print("\n=== 压缩完整性验证 ===")
    
    try:
        # 1. 解压数据
        decompressed_bytes = gzip.decompress(gzip_data)
        decompressed_str = decompressed_bytes.decode('utf-8')
        
        # 2. 比较内容
        if decompressed_str != original_json:
            print("❌ 解压内容与原始内容不匹配")
            print(f"原始长度: {len(original_json)}")
            print(f"解压长度: {len(decompressed_str)}")
            return False
        
        # 3. 验证ISIZE
        isize = extract_isize(gzip_data)
        actual_size = len(original_json.encode('utf-8'))
        
        if isize != actual_size:
            print("❌ ISIZE验证失败")
            print(f"ISIZE: {isize}")
            print(f"实际: {actual_size}")
            return False
        
        print("✅ 压缩完整性验证通过")
        print(f"压缩前: {actual_size} 字节")
        print(f"压缩后: {len(gzip_data)} 字节")
        print(f"压缩比: {actual_size / len(gzip_data):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_compression_levels(json_data):
    """测试不同压缩级别"""
    print("\n=== 测试不同压缩级别 ===")
    utf8_bytes = json_data.encode('utf-8')
    
    results = []
    for level in range(1, 10):
        compressed = gzip.compress(utf8_bytes, compresslevel=level)
        ratio = len(utf8_bytes) / len(compressed)
        results.append((level, len(compressed), ratio))
        print(f"级别{level}: {len(compressed)}字节 (压缩比: {ratio:.2f})")
    
    return results

def find_closest_to_server(results, server_size=3120):
    """找到最接近服务器大小的压缩级别"""
    print(f"\n=== 寻找最接近服务器大小({server_size}字节)的级别 ===")
    
    closest_level = None
    min_diff = float('inf')
    
    for level, size, ratio in results:
        diff = abs(size - server_size)
        print(f"级别{level}: 差异 {diff} 字节")
        
        if diff < min_diff:
            min_diff = diff
            closest_level = level
    
    print(f"✅ 最接近的是级别{closest_level}，差异{min_diff}字节")
    return closest_level

def analyze_json_differences(json_data):
    """分析JSON的详细信息"""
    print("\n=== JSON详细分析 ===")

    # 基本信息
    utf8_bytes = json_data.encode('utf-8')
    print(f"字符数: {len(json_data)}")
    print(f"UTF-8字节数: {len(utf8_bytes)}")

    # 字符分布统计
    ascii_count = 0
    chinese_count = 0
    special_count = 0

    for char in json_data:
        code = ord(char)
        if code < 128:
            ascii_count += 1
        elif 0x4E00 <= code <= 0x9FFF:  # 中文字符范围
            chinese_count += 1
        else:
            special_count += 1

    print(f"ASCII字符: {ascii_count}个")
    print(f"中文字符: {chinese_count}个")
    print(f"特殊字符: {special_count}个")

    # 预测字节数
    predicted_bytes = ascii_count + chinese_count * 3 + special_count * 2
    print(f"预测UTF-8字节数: {predicted_bytes}")
    print(f"实际UTF-8字节数: {len(utf8_bytes)}")
    print(f"预测准确性: {'✅' if predicted_bytes == len(utf8_bytes) else '❌'}")

    # 检查特殊字符
    special_chars = set()
    for char in json_data:
        if ord(char) >= 128:
            special_chars.add(char)

    print(f"发现的特殊字符: {sorted(special_chars)}")

    # 与服务器声明对比
    print(f"\n=== 与服务器对比 ===")
    print(f"服务器Content-Length: 10983字节 (之前验证的)")
    print(f"当前JSON UTF-8大小: {len(utf8_bytes)}字节")
    print(f"差异: {len(utf8_bytes) - 10983}字节")

    return utf8_bytes

def test_different_formats(json_data):
    """测试不同格式的影响"""
    print("\n=== 测试不同格式影响 ===")

    # 原始格式
    original_size = len(json_data.encode('utf-8'))
    print(f"原始格式: {original_size}字节")

    # 去除所有空格
    no_spaces = json_data.replace(' ', '')
    no_spaces_size = len(no_spaces.encode('utf-8'))
    print(f"去除空格: {no_spaces_size}字节 (减少{original_size - no_spaces_size}字节)")

    # 格式化JSON
    try:
        import json
        parsed = json.loads(json_data)
        formatted = json.dumps(parsed, ensure_ascii=False, separators=(',', ':'))
        formatted_size = len(formatted.encode('utf-8'))
        print(f"紧凑格式: {formatted_size}字节 (减少{original_size - formatted_size}字节)")

        # 美化格式
        pretty = json.dumps(parsed, ensure_ascii=False, indent=2)
        pretty_size = len(pretty.encode('utf-8'))
        print(f"美化格式: {pretty_size}字节 (增加{pretty_size - original_size}字节)")

    except Exception as e:
        print(f"JSON解析失败: {e}")

def main():
    # 你的JSON数据
    json_data = '''{"LevelOrderList":[{"px":1,"UserID":5491031,"SerialNo":"11017649818871136055","IsPub":1,"ZoneServerID":"110104317255600","Title":"段位：暗部3阶-超影1阶 2S1A","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":10,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":100,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.00},{"px":2,"UserID":5376429,"SerialNo":"11017641628995604521","IsPub":1,"ZoneServerID":"110104317255600","Title":"【影1 52分-超影】 配置低来大佬 1s4a 战绩好超时不计","Price":16.0000,"Ensure1":15.0000,"Ensure2":15.0000,"Ensure":30.0000,"TimeLimit":24,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":47,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.39},{"px":3,"UserID":6779067,"SerialNo":"11017633663077532404","IsPub":1,"ZoneServerID":"110104117222100","Title":"百忍大战7冠扫码上号，安卓微信","Price":7.0000,"Ensure1":3.0000,"Ensure2":3.0000,"Ensure":6.0000,"TimeLimit":15,"Stamp":0,"Create":"新手USR201911020839","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":5,"UserID":5993179,"SerialNo":"11017649596870325924","IsPub":1,"ZoneServerID":"110104017182700","Title":"扫码 暗部1-影级1 3s 9a","Price":3.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":15,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":10.00},{"px":6,"UserID":23705152,"SerialNo":"11017648253291080683","IsPub":1,"ZoneServerID":"110104017182700","Title":"【指定永恒佐助一局35块】","Price":3.0000,"Ensure1":60.0000,"Ensure2":60.0000,"Ensure":120.0000,"TimeLimit":50,"Stamp":0,"Create":"新手USR2025080407066","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":""},{"px":7,"UserID":18205529,"SerialNo":"11017650091579216525","IsPub":1,"ZoneServerID":"110104317255600","Title":"上忍2阶-超影【14s30a】","Price":15.0000,"Ensure1":8.0000,"Ensure2":8.0000,"Ensure":16.0000,"TimeLimit":34,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":14.56},{"px":8,"UserID":5993179,"SerialNo":"11017649643981411737","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍1-影级1  2S 13A","Price":5.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":27,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":12.82},{"px":9,"UserID":5491031,"SerialNo":"11017649205725358022","IsPub":1,"ZoneServerID":"110104117222100","Title":"影级2阶-超影1 3s4a","Price":16.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":14,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":9,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":30.00},{"px":10,"UserID":23700204,"SerialNo":"11017650117379773935","IsPub":1,"ZoneServerID":"110104017182700","Title":"█别接破单了█一局35块,来打手█看要求都是高价","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2400,"Stamp":0,"Create":"新手USR2025080402197","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":11,"UserID":5993179,"SerialNo":"11017648446713332547","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 1s 8a","Price":25.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.77},{"px":12,"UserID":20332880,"SerialNo":"11018037735692841425","IsPub":1,"ZoneServerID":"110104017182700","Title":"巅峰前4","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":2,"Stamp":1992519617,"Create":"新手USR2024032405655","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":13,"UserID":13924284,"SerialNo":"11017649935881854223","IsPub":1,"ZoneServerID":"110104217241000","Title":"指定冰墩墩，套餐1打90天","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2160,"Stamp":0,"Create":"小喇叭发单","SameCity":"","SettleHour":1,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":14,"UserID":5376429,"SerialNo":"11017643783357962828","IsPub":1,"ZoneServerID":"110104017182700","Title":"【武当花火忍道分1622分到2250分】 角色名后是号主手机 联系号主扫码上号","Price":20.0000,"Ensure1":14.0000,"Ensure2":14.0000,"Ensure":28.0000,"TimeLimit":30,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":46,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":28.30},{"px":15,"UserID":5874699,"SerialNo":"11017613436873156575","IsPub":1,"ZoneServerID":"110104317255600","Title":"中忍2到超影（没实力别来，中途退，超时0结算）","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":3,"Stamp":0,"Create":"记得多喝热水。","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":13.64},{"px":16,"UserID":21716850,"SerialNo":"11017638838775995216","IsPub":1,"ZoneServerID":"110104217241000","Title":"苹果 影1到超 月初难打来个26选手 中途取消或接单不联系我扣钱","Price":14.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":5,"Stamp":0,"Create":"小河发单秒验收","SameCity":"","SettleHour":19,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.90},{"px":17,"UserID":23468051,"SerialNo":"11017644863363988536","IsPub":1,"ZoneServerID":"110104117222100","Title":"有需要打V团本的老板dd","Price":2.0000,"Ensure1":2.0000,"Ensure2":2.0000,"Ensure":4.0000,"TimeLimit":24,"Stamp":0,"Create":"新手USR2025071205302","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":18,"UserID":5993179,"SerialNo":"11017648444911673393","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3 36分-超影2500分 指定宇智波佐助 春野樱 漩涡鸣人","Price":66.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":80,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":46.81},{"px":19,"UserID":5993179,"SerialNo":"11017648444157533816","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3-超影  5s8a","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":52,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.00},{"px":20,"UserID":5993179,"SerialNo":"11017648883937125814","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 6s20a","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":18.56}],"RecordCount":29,"HasNew":0}'''
    
    print("=== 模拟服务器gzip压缩验证程序 ===")

    # 0. 详细分析JSON
    analyze_json_differences(json_data)
    test_different_formats(json_data)

    print(f"\n服务器声明Content-Length: 3120字节")

    # 1. 模拟服务器压缩（默认级别6）
    server_compressed = compress_like_server(json_data)

    # 2. 对比结果
    print(f"\n=== 对比结果 ===")
    print(f"服务器声明: 3120字节")
    print(f"我们的压缩: {len(server_compressed)}字节")
    print(f"差异: {len(server_compressed) - 3120}字节")

    if abs(len(server_compressed) - 3120) <= 100:
        print("✅ 压缩大小接近服务器声明 (差异在100字节内)")
    else:
        print("❌ 压缩大小与服务器声明差异较大")

    # 3. 验证压缩完整性
    verify_compression(server_compressed, json_data)

    # 4. 测试不同压缩级别
    results = test_compression_levels(json_data)

    # 5. 找到最接近服务器的级别
    closest_level = find_closest_to_server(results, 3120)

    # 6. 使用最接近的级别重新压缩
    if closest_level:
        print(f"\n=== 使用最佳级别{closest_level}重新压缩 ===")
        best_compressed = compress_like_server(json_data, closest_level)
        verify_compression(best_compressed, json_data)

        # 保存压缩文件
        with open('server_compressed.gz', 'wb') as f:
            f.write(best_compressed)
        print(f"✅ 压缩文件已保存为 server_compressed.gz")
        print(f"可以与你的在线压缩结果对比")

    # 7. 总结服务器算法
    print(f"\n=== 服务器算法总结 ===")
    print(f"✅ 服务器使用UTF-8字节数计算Content-Length")
    print(f"✅ ISIZE字段验证是最可靠的完整性检查")
    print(f"✅ 不需要依赖Content-Length进行gzip验证")
    print(f"⚠️  压缩大小差异可能来自不同的压缩参数或额外数据")

if __name__ == "__main__":
    main()
