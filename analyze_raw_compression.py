#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import struct
import binascii

def analyze_http_headers(headers_text):
    """分析HTTP响应头"""
    print("=== HTTP响应头分析 ===")
    
    headers = {}
    for line in headers_text.strip().split('\n'):
        if ':' in line:
            key, value = line.split(':', 1)
            headers[key.strip().lower()] = value.strip()
    
    # 关键头部信息
    content_length = headers.get('content-length', 'N/A')
    content_encoding = headers.get('content-encoding', 'N/A')
    content_type = headers.get('content-type', 'N/A')
    server = headers.get('server', 'N/A')
    
    print(f"Content-Length: {content_length}")
    print(f"Content-Encoding: {content_encoding}")
    print(f"Content-Type: {content_type}")
    print(f"Server: {server}")
    
    return headers

def analyze_gzip_data(hex_data):
    """分析gzip压缩数据"""
    print("\n=== gzip数据分析 ===")
    
    # 将十六进制字符串转换为字节
    try:
        # 移除空格和换行符
        clean_hex = ''.join(hex_data.split())
        binary_data = binascii.unhexlify(clean_hex)
        
        print(f"压缩数据大小: {len(binary_data)}字节")
        
        # 检查gzip魔数
        if len(binary_data) >= 2:
            magic = binary_data[:2]
            if magic == b'\x1f\x8b':
                print("✅ 检测到有效的gzip魔数")
            else:
                print(f"❌ 无效的gzip魔数: {magic.hex()}")
                return None
        
        # 分析gzip头部
        if len(binary_data) >= 10:
            header = binary_data[:10]
            magic, method, flags, mtime, xfl, os = struct.unpack('<HBBLBB', header)
            
            print(f"gzip头部信息:")
            print(f"  魔数: 0x{magic:04x}")
            print(f"  压缩方法: {method} ({'DEFLATE' if method == 8 else '未知'})")
            print(f"  标志: 0x{flags:02x}")
            print(f"  修改时间: {mtime}")
            print(f"  额外标志: 0x{xfl:02x}")
            print(f"  操作系统: {os}")
        
        # 分析gzip尾部（ISIZE和CRC32）
        if len(binary_data) >= 8:
            trailer = binary_data[-8:]
            crc32, isize = struct.unpack('<LL', trailer)
            
            print(f"gzip尾部信息:")
            print(f"  CRC32: 0x{crc32:08x}")
            print(f"  ISIZE: {isize}字节 (原始数据大小)")
        
        # 尝试解压
        try:
            decompressed = gzip.decompress(binary_data)
            print(f"✅ 解压成功: {len(decompressed)}字节")
            
            # 验证ISIZE
            if len(binary_data) >= 8:
                if len(decompressed) == isize:
                    print("✅ ISIZE验证通过")
                else:
                    print(f"❌ ISIZE验证失败: 实际{len(decompressed)}字节, ISIZE{isize}字节")
            
            # 显示解压后的数据
            try:
                json_text = decompressed.decode('utf-8')
                print(f"解压后的JSON:")
                print(f"  字符数: {len(json_text)}")
                print(f"  UTF-8字节数: {len(decompressed)}")
                print(f"  开头: {json_text[:100]}...")
                print(f"  结尾: ...{json_text[-100:]}")
                
                # 验证JSON格式
                import json
                try:
                    parsed = json.loads(json_text)
                    print("✅ JSON格式验证通过")
                    
                    # 提取关键信息
                    if 'RecordCount' in parsed:
                        record_count = parsed['RecordCount']
                        print(f"RecordCount: {record_count}")
                        
                        # 验证我们的理论
                        if record_count % 2 == 0:
                            print(f"RecordCount是偶数，理论预测Content-Length: 2932-3114字节")
                        else:
                            print(f"RecordCount是奇数，理论预测Content-Length: 3093-3186字节")
                    
                    if 'LevelOrderList' in parsed:
                        level_list = parsed['LevelOrderList']
                        print(f"实际记录数: {len(level_list)}")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON格式错误: {e}")
                    
            except UnicodeDecodeError as e:
                print(f"❌ UTF-8解码失败: {e}")
                
        except Exception as e:
            print(f"❌ gzip解压失败: {e}")
            
        return binary_data
        
    except Exception as e:
        print(f"❌ 十六进制数据解析失败: {e}")
        return None

def compare_with_theory(content_length, record_count, json_size, gzip_size):
    """对比理论预测"""
    print(f"\n=== 理论对比分析 ===")
    
    print(f"实际数据:")
    print(f"  Content-Length: {content_length}字节")
    print(f"  RecordCount: {record_count}")
    print(f"  JSON UTF-8大小: {json_size}字节")
    print(f"  gzip压缩大小: {gzip_size}字节")
    print(f"  包装开销: {content_length - gzip_size}字节")
    print(f"  压缩比: {gzip_size/json_size:.1%}")
    
    # 验证奇偶性理论
    if record_count % 2 == 0:
        predicted_range = "2932-3114字节"
        in_range = 2932 <= content_length <= 3114
    else:
        predicted_range = "3093-3186字节"
        in_range = 3093 <= content_length <= 3186
    
    print(f"\n奇偶性理论验证:")
    print(f"  预测范围: {predicted_range}")
    print(f"  实际结果: {content_length}字节")
    print(f"  理论正确: {'✅' if in_range else '❌'}")

def main():
    print("=== 原始压缩数据分析工具 ===")
    print("请按照以下格式提供数据:")
    print("1. HTTP响应头 (每行一个Header)")
    print("2. 空行")
    print("3. 十六进制压缩数据")
    print()
    
    # 示例用法
    print("示例格式:")
    print("Content-Length: 3114")
    print("Content-Encoding: gzip")
    print("Content-Type: text/plain; charset=utf-8")
    print()
    print("1f8b080000000000000...")
    print()
    
    # 等待用户输入
    print("请粘贴你的数据 (输入'END'结束):")
    
    lines = []
    while True:
        line = input()
        if line.strip() == 'END':
            break
        lines.append(line)
    
    # 分离头部和数据
    headers_lines = []
    hex_lines = []
    in_headers = True
    
    for line in lines:
        if line.strip() == '':
            in_headers = False
            continue
        
        if in_headers:
            headers_lines.append(line)
        else:
            hex_lines.append(line)
    
    # 分析数据
    if headers_lines:
        headers = analyze_http_headers('\n'.join(headers_lines))
        content_length = int(headers.get('content-length', 0))
    else:
        content_length = 0
    
    if hex_lines:
        hex_data = '\n'.join(hex_lines)
        binary_data = analyze_gzip_data(hex_data)
        
        if binary_data:
            # 如果解压成功，进行理论对比
            try:
                decompressed = gzip.decompress(binary_data)
                json_text = decompressed.decode('utf-8')
                
                import json
                parsed = json.loads(json_text)
                record_count = parsed.get('RecordCount', 0)
                
                compare_with_theory(content_length, record_count, len(decompressed), len(binary_data))
                
            except:
                pass

if __name__ == "__main__":
    main()
