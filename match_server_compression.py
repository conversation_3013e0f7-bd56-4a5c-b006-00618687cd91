#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import gzip
import zlib
import struct

# 你的JSON数据
json_data = '''{"LevelOrderList":[{"px":1,"UserID":23700204,"SerialNo":"11017650117379773935","IsPub":1,"ZoneServerID":"110104017182700","Title":"█别接破单了█一局35块,来打手█看要求都是高价","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2400,"Stamp":0,"Create":"新手USR2025080402197","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":""},{"px":2,"UserID":11353372,"SerialNo":"11017602067088687893","IsPub":1,"ZoneServerID":"110104317255600","Title":"AC、 百忍全奖励","Price":20.0000,"Ensure1":10.0000,"Ensure2":10.0000,"Ensure":20.0000,"TimeLimit":12,"Stamp":0,"Create":"火影蔡徐坤","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":3,"UserID":21716850,"SerialNo":"11017638838775995216","IsPub":1,"ZoneServerID":"110104217241000","Title":"苹果 影1到超 月初难打来个26选手 中途取消或接单不联系我扣钱","Price":14.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":5,"Stamp":0,"Create":"小河发单秒验收","SameCity":"","SettleHour":19,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.90},{"px":4,"UserID":5993179,"SerialNo":"11017648444157533816","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3-超影  5s8a","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":52,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.00},{"px":5,"UserID":5993179,"SerialNo":"11017648446713332547","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 1s 8a","Price":25.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.77},{"px":6,"UserID":23468051,"SerialNo":"11017644863363988536","IsPub":1,"ZoneServerID":"110104117222100","Title":"有需要打V团本的老板dd","Price":2.0000,"Ensure1":2.0000,"Ensure2":2.0000,"Ensure":4.0000,"TimeLimit":24,"Stamp":0,"Create":"新手USR2025071205302","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":7,"UserID":5993179,"SerialNo":"11017649643981411737","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍1-影级1  2S 13A","Price":5.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":27,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":12.82},{"px":8,"UserID":6779067,"SerialNo":"11017633663077532404","IsPub":1,"ZoneServerID":"110104117222100","Title":"百忍大战7冠扫码上号，安卓微信","Price":7.0000,"Ensure1":3.0000,"Ensure2":3.0000,"Ensure":6.0000,"TimeLimit":15,"Stamp":0,"Create":"新手USR201911020839","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":9,"UserID":7032583,"SerialNo":"11017649234449896060","IsPub":1,"ZoneServerID":"110104317255600","Title":"一个月日常加周胜，秘境探险跟团本，每天日常能搞的都要搞","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":2400,"Stamp":0,"Create":"ε?(?> ? <)?","SameCity":"","SettleHour":48,"SitePrice":"","Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":11,"UserID":5376429,"SerialNo":"11017641628995604521","IsPub":1,"ZoneServerID":"110104317255600","Title":"【影1 52分-超影】 配置低来大佬 1s4a 战绩好超时不计","Price":16.0000,"Ensure1":15.0000,"Ensure2":15.0000,"Ensure":30.0000,"TimeLimit":24,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":47,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.39},{"px":12,"UserID":18205529,"SerialNo":"11017650091579216525","IsPub":1,"ZoneServerID":"110104317255600","Title":"上忍2阶-超影【14s30a】","Price":15.0000,"Ensure1":8.0000,"Ensure2":8.0000,"Ensure":16.0000,"TimeLimit":34,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":14.56},{"px":14,"UserID":5376429,"SerialNo":"11017643783357962828","IsPub":1,"ZoneServerID":"110104017182700","Title":"【武当花火忍道分1622分到2250分】 角色名后是号主手机 联系号主扫码上号","Price":20.0000,"Ensure1":14.0000,"Ensure2":14.0000,"Ensure":28.0000,"TimeLimit":30,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":46,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":28.30},{"px":15,"UserID":22049761,"SerialNo":"11018267638501169537","IsPub":1,"ZoneServerID":"110104017182700","Title":"更多玩法全部宝箱＋超影","Price":2.0000,"Ensure1":25.0000,"Ensure2":25.0000,"Ensure":50.0000,"TimeLimit":2,"Stamp":2097547561,"Create":"新手USR2025011103301","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":16,"UserID":18205529,"SerialNo":"11017650162378137164","IsPub":1,"ZoneServerID":"110104017182700","Title":"★ 暗部3阶-超影1】3s10a/接单电话通知","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":50,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.50},{"px":17,"UserID":5993179,"SerialNo":"11017648883937125814","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 6s20a","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":18.56},{"px":18,"UserID":5874699,"SerialNo":"11017613436873156575","IsPub":1,"ZoneServerID":"110104317255600","Title":"中忍2到超影（没实力别来，中途退，超时0结算）","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":3,"Stamp":0,"Create":"记得多喝热水。","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":13.64},{"px":19,"UserID":13924284,"SerialNo":"11017649935881854223","IsPub":1,"ZoneServerID":"110104217241000","Title":"指定冰墩墩，套餐1打90天","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2160,"Stamp":0,"Create":"小喇叭发单","SameCity":"","SettleHour":1,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":20,"UserID":5993179,"SerialNo":"11017649596870325924","IsPub":1,"ZoneServerID":"110104017182700","Title":"扫码 暗部1-影级1 3s 9a","Price":3.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":15,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":10.00}],"RecordCount":27,"HasNew":0}'''

def test_different_compression_methods():
    """测试不同的压缩方法"""
    print("=== 尝试匹配服务器压缩大小 ===")
    print(f"目标: 3154字节")
    
    utf8_data = json_data.encode('utf-8')
    print(f"原始UTF-8大小: {len(utf8_data)}字节")
    
    results = []
    
    # 1. 标准gzip压缩（不同级别）
    print("\n--- 标准gzip压缩 ---")
    for level in range(1, 10):
        compressed = gzip.compress(utf8_data, compresslevel=level)
        diff = abs(len(compressed) - 3154)
        results.append(('gzip', level, len(compressed), diff))
        print(f"gzip级别{level}: {len(compressed)}字节 (差异: {diff})")
    
    # 2. 原始deflate压缩
    print("\n--- 原始deflate压缩 ---")
    for level in range(1, 10):
        compressed = zlib.compress(utf8_data, level)
        diff = abs(len(compressed) - 3154)
        results.append(('deflate', level, len(compressed), diff))
        print(f"deflate级别{level}: {len(compressed)}字节 (差异: {diff})")
    
    # 3. 自定义gzip（模拟ASP.NET）
    print("\n--- 自定义gzip（模拟ASP.NET） ---")
    try:
        # 模拟ASP.NET的GZipStream行为
        for level in range(1, 10):
            compressed = create_aspnet_like_gzip(utf8_data, level)
            diff = abs(len(compressed) - 3154)
            results.append(('aspnet-gzip', level, len(compressed), diff))
            print(f"ASP.NET-like级别{level}: {len(compressed)}字节 (差异: {diff})")
    except Exception as e:
        print(f"ASP.NET模拟失败: {e}")
    
    # 4. 添加填充数据
    print("\n--- 添加填充数据 ---")
    best_gzip = gzip.compress(utf8_data, compresslevel=1)  # 2326字节
    needed_padding = 3154 - len(best_gzip)  # 828字节
    print(f"最佳gzip: {len(best_gzip)}字节")
    print(f"需要填充: {needed_padding}字节")
    
    # 尝试不同的填充方式
    padding_tests = [
        ("空字节", b'\x00' * needed_padding),
        ("随机字节", b'\x01\x02\x03\x04' * (needed_padding // 4)),
        ("HTTP头模拟", b'HTTP/1.1 200 OK\r\n' * (needed_padding // 17)),
    ]
    
    for name, padding in padding_tests:
        padded = best_gzip + padding
        print(f"{name}填充: {len(padded)}字节 (差异: {abs(len(padded) - 3154)})")
    
    # 5. 找到最接近的结果
    print("\n=== 最接近的结果 ===")
    results.sort(key=lambda x: x[3])  # 按差异排序
    
    for i, (method, level, size, diff) in enumerate(results[:5]):
        print(f"{i+1}. {method}级别{level}: {size}字节 (差异: {diff})")
    
    return results

def create_aspnet_like_gzip(data, level):
    """模拟ASP.NET的GZipStream压缩"""
    # 使用zlib创建自定义gzip格式
    compressor = zlib.compressobj(
        level=level,
        method=zlib.DEFLATED,
        wbits=15 + 16,  # gzip格式
        memLevel=8,
        strategy=zlib.Z_DEFAULT_STRATEGY
    )
    
    compressed_data = compressor.compress(data)
    compressed_data += compressor.flush()
    
    return compressed_data

def analyze_size_difference():
    """分析大小差异的可能原因"""
    print("\n=== 分析大小差异 ===")
    
    utf8_data = json_data.encode('utf-8')
    our_compressed = gzip.compress(utf8_data, compresslevel=1)
    
    print(f"我们的压缩: {len(our_compressed)}字节")
    print(f"服务器声明: 3154字节")
    print(f"差异: {3154 - len(our_compressed)}字节")
    
    # 分析可能的额外数据
    extra_bytes = 3154 - len(our_compressed)
    print(f"\n可能的额外数据 ({extra_bytes}字节):")
    print(f"- HTTP chunk标记: ~10-50字节")
    print(f"- 传输编码开销: ~20-100字节")
    print(f"- ASP.NET特有头部: ~50-200字节")
    print(f"- 其他元数据: ~{extra_bytes - 200}字节")
    
    # 检查gzip头部差异
    print(f"\n我们的gzip头部: {our_compressed[:10].hex()}")
    print(f"标准gzip头部应该是: 1f8b08...")

def test_chunked_encoding():
    """测试HTTP分块编码的影响"""
    print("\n=== 测试HTTP分块编码 ===")
    
    utf8_data = json_data.encode('utf-8')
    compressed = gzip.compress(utf8_data, compresslevel=1)
    
    # 模拟HTTP分块编码
    chunk_size = len(compressed)
    chunk_header = f"{chunk_size:x}\r\n".encode()
    chunk_trailer = b"\r\n0\r\n\r\n"
    
    chunked_data = chunk_header + compressed + chunk_trailer
    
    print(f"原始压缩: {len(compressed)}字节")
    print(f"分块头部: {len(chunk_header)}字节 ({chunk_header})")
    print(f"分块尾部: {len(chunk_trailer)}字节")
    print(f"分块总大小: {len(chunked_data)}字节")
    print(f"与服务器差异: {abs(len(chunked_data) - 3154)}字节")

if __name__ == "__main__":
    # 测试不同压缩方法
    results = test_different_compression_methods()
    
    # 分析大小差异
    analyze_size_difference()
    
    # 测试分块编码
    test_chunked_encoding()
    
    print(f"\n=== 结论 ===")
    print(f"✅ ISIZE验证方法依然是最可靠的")
    print(f"✅ 不需要匹配Content-Length进行完整性验证")
    print(f"⚠️  压缩大小差异可能来自服务器实现细节")
