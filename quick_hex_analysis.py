#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import binascii
import gzip

# 你提供的十六进制数据（截取前面部分）
hex_data = "7B224C6576656C4F726465724C697374223A5B7B227078223A322C22557365724944223A353337363432392C2253657269616C4E6F223A223131303137363530333636343131303631373935222C224973507562223A312C225A6F6E655365727665724944223A22313130313034303137313832373030222C225469746C65223A22E38090E69DA5E78C9BE794B7E38091E69A97E983A833203334E58886E588B0E8B685E5BDB120E68C87E5AE9AE9B9B0E4BD9020E9AAA8E69EB620E696B0E698A5E68891E788B1E7BD972020E8A792E889B2E5908DE5908EE698AFE58FB7E4B8BBE6898BE69CBA20E88194E7B3BBE58FB7E4B8BBE689ABE7A081E4B88AE58FB7222C225072696365223A31382E303030302C22456E7375726531223A31352E303030302C22456E7375726532223A31352E303030302C22456E73757265223A33302E303030302C2254696D654C696D6974223A32342C225374616D70223A302C22437265617465223A22E4BCAAE4BBA3E8AFB7E5BC80E5A78BE4BDA0E79A84E8A1A8E6BC94EFBC81222C2253616D6543697479223A22222C22536574746C65486F7572223A34352C22536974655072696365223A302E30302C2247616D65223A22E781ABE5BDB1E5BF8DE88085222C225A6F6E65223A22E5AE89E58D935151222C22536572766572223A22E9BB98E8AEA4E69C8D222C22466F6375736564223A312C22556E69745072696365223A302E303030302C2254616773223A22222C2254616773547874223A22222C224F7264657254797065223A302C22496E476F6F645072696365223A302C224973417070223A302C22496E4E6F456E737572654C697374223A302C224C6576656C5479706532223A31302C225265776172644D6F6E6579223A302C22507269506172746E6572223A302C22476F6F6450726F706F7274696F6E223A31382E35367D"

print("=== 快速十六进制分析 ===")

# 解析十六进制
binary_data = binascii.unhexlify(hex_data)
print(f"数据大小: {len(binary_data)}字节")

# 检查是否是gzip
if len(binary_data) >= 2:
    magic = binary_data[:2]
    if magic == b'\x1f\x8b':
        print("✅ 这是gzip数据")
    else:
        print("❌ 这不是gzip数据")
        print(f"开头字节: {binary_data[:10].hex()}")

# 尝试UTF-8解码
try:
    text = binary_data.decode('utf-8')
    print(f"✅ UTF-8解码成功")
    print(f"文本长度: {len(text)}字符")
    print(f"开头: {text[:100]}")
    
    # 查找RecordCount
    import re
    match = re.search(r'"RecordCount":(\d+)', text)
    if match:
        record_count = int(match.group(1))
        print(f"RecordCount: {record_count}")
    else:
        print("未找到RecordCount")
        
except Exception as e:
    print(f"❌ UTF-8解码失败: {e}")

# 测试压缩
if 'text' in locals():
    utf8_data = text.encode('utf-8')
    compressed = gzip.compress(utf8_data, compresslevel=6)
    print(f"\n压缩分析:")
    print(f"原始大小: {len(utf8_data)}字节")
    print(f"压缩后: {len(compressed)}字节")
    print(f"压缩比: {len(compressed)/len(utf8_data):.1%}")

print(f"\n🎯 结论:")
print(f"这是明文JSON数据，不是gzip压缩数据")
print(f"需要确认对应的HTTP响应头信息")
