// 数据完整性检查工具
// 用于验证压缩数据是否完整，没有截断

#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonParseError>
#include <QCryptographicHash>
#include <QDebug>
#include "src/core/utils/logger.h"

class DataIntegrityChecker
{
public:
    struct IntegrityResult {
        bool isComplete = false;
        bool isValidJson = false;
        bool hasCorrectStructure = false;
        int originalLength = 0;
        int processedLength = 0;
        QString errorMessage;
        QString dataHash;
        double compressionRatio = 0.0;
    };

    // 检查JSON数据完整性
    static IntegrityResult checkJsonIntegrity(const QString& jsonData)
    {
        IntegrityResult result;
        result.originalLength = jsonData.length();
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 开始数据完整性检查 ===");
        
        // 1. 基本长度检查
        if (jsonData.isEmpty()) {
            result.errorMessage = "数据为空";
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "❌ " + result.errorMessage);
            return result;
        }
        
        // 2. 去除首尾空白字符
        QString trimmedData = jsonData.trimmed();
        result.processedLength = trimmedData.length();
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("📏 数据长度: 原始=%1, 处理后=%2")
                    .arg(result.originalLength).arg(result.processedLength));
        
        // 3. 检查JSON结构
        if (trimmedData.startsWith('{') && trimmedData.endsWith('}')) {
            result.hasCorrectStructure = true;
            NEW_LOG_INFO(NewLogCategory::NETWORK, "✅ JSON对象结构正确 (以{}包围)");
        } else if (trimmedData.startsWith('[') && trimmedData.endsWith(']')) {
            result.hasCorrectStructure = true;
            NEW_LOG_INFO(NewLogCategory::NETWORK, "✅ JSON数组结构正确 (以[]包围)");
        } else {
            result.errorMessage = "JSON结构不完整 - 缺少正确的开头或结尾";
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "❌ " + result.errorMessage);
            return result;
        }
        
        // 4. JSON解析验证
        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(trimmedData.toUtf8(), &parseError);
        
        if (parseError.error != QJsonParseError::NoError) {
            result.errorMessage = QString("JSON解析失败: %1 (位置: %2)")
                                .arg(parseError.errorString())
                                .arg(parseError.offset);
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "❌ " + result.errorMessage);
            
            // 显示错误位置附近的内容
            int errorPos = parseError.offset;
            int start = qMax(0, errorPos - 50);
            int end = qMin(trimmedData.length(), errorPos + 50);
            QString context = trimmedData.mid(start, end - start);
            NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("错误位置附近: ...%1...").arg(context));
            
            return result;
        }
        
        result.isValidJson = true;
        NEW_LOG_INFO(NewLogCategory::NETWORK, "✅ JSON解析成功");
        
        // 5. 重新序列化验证
        QString reserializedJson = doc.toJson(QJsonDocument::Compact);
        if (!reserializedJson.isEmpty()) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("✅ JSON重新序列化成功，长度: %1字符")
                        .arg(reserializedJson.length()));
        }
        
        // 6. 计算数据哈希
        QByteArray dataBytes = trimmedData.toUtf8();
        QByteArray hash = QCryptographicHash::hash(dataBytes, QCryptographicHash::Md5);
        result.dataHash = hash.toHex();
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("🔐 数据MD5哈希: %1").arg(result.dataHash));
        
        // 7. 压缩率测试
        QByteArray compressed = qCompress(dataBytes);
        if (!compressed.isEmpty()) {
            result.compressionRatio = (double)compressed.size() / dataBytes.size() * 100.0;
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("📊 本地压缩测试: 原始=%1字节, 压缩后=%2字节, 压缩率=%.1f%%")
                        .arg(dataBytes.size()).arg(compressed.size()).arg(result.compressionRatio));
        }
        
        // 8. 内容分析
        if (doc.isObject()) {
            QJsonObject obj = doc.object();
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("📋 JSON对象包含 %1 个键").arg(obj.size()));
            
            // 显示主要键名
            QStringList keys = obj.keys();
            if (keys.size() <= 10) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("🔑 主要键名: %1").arg(keys.join(", ")));
            } else {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("🔑 前10个键名: %1...").arg(keys.mid(0, 10).join(", ")));
            }
        } else if (doc.isArray()) {
            QJsonArray arr = doc.array();
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("📋 JSON数组包含 %1 个元素").arg(arr.size()));
        }
        
        result.isComplete = true;
        NEW_LOG_INFO(NewLogCategory::NETWORK, "✅ 数据完整性检查通过");
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 数据完整性检查完成 ===");
        
        return result;
    }
    
    // 检查数据是否被截断
    static bool checkForTruncation(const QString& data)
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 检查数据截断 ===");
        
        QString trimmed = data.trimmed();
        
        // 检查常见的截断模式
        QStringList truncationPatterns = {
            "...",           // 省略号
            "truncated",     // 截断标记
            "incomplete",    // 不完整标记
            "[cut]",         // 切断标记
            "...[more]"      // 更多内容标记
        };
        
        for (const QString& pattern : truncationPatterns) {
            if (trimmed.contains(pattern, Qt::CaseInsensitive)) {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("⚠️ 发现可能的截断标记: %1").arg(pattern));
                return true;
            }
        }
        
        // 检查JSON结构完整性
        if (trimmed.startsWith('{') && !trimmed.endsWith('}')) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, "⚠️ JSON对象可能被截断 (有开头无结尾)");
            return true;
        }
        
        if (trimmed.startsWith('[') && !trimmed.endsWith(']')) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, "⚠️ JSON数组可能被截断 (有开头无结尾)");
            return true;
        }
        
        // 检查是否以不完整的JSON结构结尾
        if (trimmed.endsWith(',') || trimmed.endsWith(':')) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, "⚠️ 数据可能被截断 (以逗号或冒号结尾)");
            return true;
        }
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "✅ 未发现截断迹象");
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 截断检查完成 ===");
        
        return false;
    }
    
    // 生成完整性报告
    static void generateIntegrityReport(const QString& data, const QString& source = "Unknown")
    {
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("=== %1 数据完整性报告 ===").arg(source));
        
        IntegrityResult result = checkJsonIntegrity(data);
        bool isTruncated = checkForTruncation(data);
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "📊 完整性报告摘要:");
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 数据来源: %1").arg(source));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 原始长度: %1字符").arg(result.originalLength));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 处理长度: %1字符").arg(result.processedLength));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - JSON有效: %1").arg(result.isValidJson ? "是" : "否"));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 结构完整: %1").arg(result.hasCorrectStructure ? "是" : "否"));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 数据完整: %1").arg(result.isComplete ? "是" : "否"));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 疑似截断: %1").arg(isTruncated ? "是" : "否"));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 数据哈希: %1").arg(result.dataHash));
        
        if (result.compressionRatio > 0) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("   - 压缩率: %.1f%%").arg(result.compressionRatio));
        }
        
        if (!result.errorMessage.isEmpty()) {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("   - 错误信息: %1").arg(result.errorMessage));
        }
        
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 报告完成 ===");
    }
};

// 使用示例:
/*
// 在您的网络请求代码中添加完整性检查
QString response = tls->executeRequest(url);
if (!response.isEmpty()) {
    DataIntegrityChecker::generateIntegrityReport(response, "API响应");
}
*/
