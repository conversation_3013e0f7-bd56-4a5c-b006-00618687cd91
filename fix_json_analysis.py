#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import binascii
import json
import re

# 大JSON的十六进制数据
hex_data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

print("=== 修复JSON分析 ===")

# 解析十六进制数据
clean_hex = ''.join(hex_data.split())
binary_data = binascii.unhexlify(clean_hex)

# 解码为UTF-8文本
json_text = binary_data.decode('utf-8')

print(f"JSON字符数: {len(json_text)}")
print(f"JSON UTF-8字节数: {len(binary_data)}")

# 显示JSON内容
print(f"\nJSON内容:")
print(f"开头: {json_text[:200]}...")
print(f"结尾: ...{json_text[-200:]}")

# 检查JSON是否完整
print(f"\n检查JSON完整性:")
print(f"以{{开头: {'✅' if json_text.startswith('{') else '❌'}")
print(f"以}}结尾: {'✅' if json_text.endswith('}') else '❌'}")

# 查找RecordCount
record_count_match = re.search(r'"RecordCount":(\d+)', json_text)
if record_count_match:
    record_count = int(record_count_match.group(1))
    print(f"RecordCount: {record_count}")
    
    # 验证奇偶性理论
    if record_count % 2 == 0:
        print(f"RecordCount是偶数，理论预测Content-Length: 2932-3114字节")
    else:
        print(f"RecordCount是奇数，理论预测Content-Length: 3093-3186字节")
else:
    print("未找到RecordCount")

# 尝试修复JSON（可能被截断了）
if not json_text.endswith('}'):
    print(f"\n尝试修复JSON...")
    # 查找最后一个完整的对象
    last_brace = json_text.rfind('}')
    if last_brace > 0:
        # 查找RecordCount和HasNew字段的位置
        record_pos = json_text.find('"RecordCount"')
        if record_pos > last_brace:
            # RecordCount在最后一个}之后，说明JSON被截断了
            # 尝试添加结尾
            fixed_json = json_text + '}'
            print(f"尝试添加结尾: {fixed_json[-50:]}")
            
            try:
                parsed = json.loads(fixed_json)
                print("✅ JSON修复成功!")
                
                if 'RecordCount' in parsed:
                    record_count = parsed['RecordCount']
                    print(f"RecordCount: {record_count}")
                
                if 'LevelOrderList' in parsed:
                    level_list = parsed['LevelOrderList']
                    print(f"实际记录数: {len(level_list)}")
                    
            except json.JSONDecodeError as e:
                print(f"❌ JSON修复失败: {e}")

# 分析压缩效果
print(f"\n=== 压缩效果分析 ===")
import gzip

utf8_data = json_text.encode('utf-8')
print(f"原始JSON UTF-8大小: {len(utf8_data)}字节")

# 测试标准gzip压缩
standard_gzip = gzip.compress(utf8_data, compresslevel=6)
print(f"标准gzip(级别6): {len(standard_gzip)}字节")
print(f"压缩比: {len(standard_gzip)/len(utf8_data):.1%}")

# 预测ASP.NET的Content-Length
base_overhead = 119  # 基于之前的分析
predicted_content_length = len(standard_gzip) + base_overhead
print(f"预测ASP.NET Content-Length: {predicted_content_length}字节")

print(f"\n🎯 关键问题:")
print(f"1. 这个JSON对应的HTTP响应的Content-Encoding是什么？")
print(f"2. 实际的Content-Length是多少？")
print(f"3. 如果是gzip压缩，应该约为{predicted_content_length}字节")
print(f"4. 如果是明文传输，应该约为{len(utf8_data)}字节")
