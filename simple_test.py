#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 简化版本的服务器算法验证

json_data = '''{"LevelOrderList":[{"px":1,"UserID":5491031,"SerialNo":"11017649818871136055","IsPub":1,"ZoneServerID":"110104317255600","Title":"段位：暗部3阶-超影1阶 2S1A","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":10,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":100,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.00},{"px":2,"UserID":5376429,"SerialNo":"11017641628995604521","IsPub":1,"ZoneServerID":"110104317255600","Title":"【影1 52分-超影】 配置低来大佬 1s4a 战绩好超时不计","Price":16.0000,"Ensure1":15.0000,"Ensure2":15.0000,"Ensure":30.0000,"TimeLimit":24,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":47,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":22.39},{"px":3,"UserID":6779067,"SerialNo":"11017633663077532404","IsPub":1,"ZoneServerID":"110104117222100","Title":"百忍大战7冠扫码上号，安卓微信","Price":7.0000,"Ensure1":3.0000,"Ensure2":3.0000,"Ensure":6.0000,"TimeLimit":15,"Stamp":0,"Create":"新手USR201911020839","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":5,"UserID":5993179,"SerialNo":"11017649596870325924","IsPub":1,"ZoneServerID":"110104017182700","Title":"扫码 暗部1-影级1 3s 9a","Price":3.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":15,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":10.00},{"px":6,"UserID":23705152,"SerialNo":"11017648253291080683","IsPub":1,"ZoneServerID":"110104017182700","Title":"【指定永恒佐助一局35块】","Price":3.0000,"Ensure1":60.0000,"Ensure2":60.0000,"Ensure":120.0000,"TimeLimit":50,"Stamp":0,"Create":"新手USR2025080407066","SameCity":"","SettleHour":0,"SitePrice":"","Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":""},{"px":7,"UserID":18205529,"SerialNo":"11017650091579216525","IsPub":1,"ZoneServerID":"110104317255600","Title":"上忍2阶-超影【14s30a】","Price":15.0000,"Ensure1":8.0000,"Ensure2":8.0000,"Ensure":16.0000,"TimeLimit":34,"Stamp":0,"Create":"团哥发单秒验收","SameCity":"","SettleHour":5,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":14.56},{"px":8,"UserID":5993179,"SerialNo":"11017649643981411737","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍1-影级1  2S 13A","Price":5.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":27,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":12.82},{"px":9,"UserID":5491031,"SerialNo":"11017649205725358022","IsPub":1,"ZoneServerID":"110104117222100","Title":"影级2阶-超影1 3s4a","Price":16.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":14,"Stamp":0,"Create":"叶叶发单","SameCity":"","SettleHour":9,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":30.00},{"px":10,"UserID":23700204,"SerialNo":"11017650117379773935","IsPub":1,"ZoneServerID":"110104017182700","Title":"█别接破单了█一局35块,来打手█看要求都是高价","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2400,"Stamp":0,"Create":"新手USR2025080402197","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":11,"UserID":5993179,"SerialNo":"11017648446713332547","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 1s 8a","Price":25.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":25.77},{"px":12,"UserID":20332880,"SerialNo":"11018037735692841425","IsPub":1,"ZoneServerID":"110104017182700","Title":"巅峰前4","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":2,"Stamp":1992519617,"Create":"新手USR2024032405655","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":13,"UserID":13924284,"SerialNo":"11017649935881854223","IsPub":1,"ZoneServerID":"110104217241000","Title":"指定冰墩墩，套餐1打90天","Price":2.0000,"Ensure1":30.0000,"Ensure2":30.0000,"Ensure":60.0000,"TimeLimit":2160,"Stamp":0,"Create":"小喇叭发单","SameCity":"","SettleHour":1,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":14,"UserID":5376429,"SerialNo":"11017643783357962828","IsPub":1,"ZoneServerID":"110104017182700","Title":"【武当花火忍道分1622分到2250分】 角色名后是号主手机 联系号主扫码上号","Price":20.0000,"Ensure1":14.0000,"Ensure2":14.0000,"Ensure":28.0000,"TimeLimit":30,"Stamp":0,"Create":"伪代请开始你的表演！","SameCity":"","SettleHour":46,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":28.30},{"px":15,"UserID":5874699,"SerialNo":"11017613436873156575","IsPub":1,"ZoneServerID":"110104317255600","Title":"中忍2到超影（没实力别来，中途退，超时0结算）","Price":15.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":3,"Stamp":0,"Create":"记得多喝热水。","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":13.64},{"px":16,"UserID":21716850,"SerialNo":"11017638838775995216","IsPub":1,"ZoneServerID":"110104217241000","Title":"苹果 影1到超 月初难打来个26选手 中途取消或接单不联系我扣钱","Price":14.0000,"Ensure1":7.0000,"Ensure2":7.0000,"Ensure":14.0000,"TimeLimit":5,"Stamp":0,"Create":"小河发单秒验收","SameCity":"","SettleHour":19,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.90},{"px":17,"UserID":23468051,"SerialNo":"11017644863363988536","IsPub":1,"ZoneServerID":"110104117222100","Title":"有需要打V团本的老板dd","Price":2.0000,"Ensure1":2.0000,"Ensure2":2.0000,"Ensure":4.0000,"TimeLimit":24,"Stamp":0,"Create":"新手USR2025071205302","SameCity":"","SettleHour":0,"SitePrice":0.00,"Game":"火影忍者","Zone":"安卓微信","Server":"默认服","Focused":0,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":2,"IsApp":1,"InNoEnsureList":1,"LevelType2":14,"RewardMoney":0,"PriPartner":0,"GoodProportion":0.00},{"px":18,"UserID":5993179,"SerialNo":"11017648444911673393","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3 36分-超影2500分 指定宇智波佐助 春野樱 漩涡鸣人","Price":66.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":80,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":46.81},{"px":19,"UserID":5993179,"SerialNo":"11017648444157533816","IsPub":1,"ZoneServerID":"110104217241000","Title":"扫码 上忍3-超影  5s8a","Price":20.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":52,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果QQ","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":20.00},{"px":20,"UserID":5993179,"SerialNo":"11017648883937125814","IsPub":1,"ZoneServerID":"110104317255600","Title":"扫码 暗部1-超影 6s20a","Price":18.0000,"Ensure1":5.0000,"Ensure2":5.0000,"Ensure":10.0000,"TimeLimit":33,"Stamp":0,"Create":"开心发单秒验收","SameCity":"","SettleHour":10,"SitePrice":0.00,"Game":"火影忍者","Zone":"苹果微信","Server":"默认服","Focused":1,"UnitPrice":0.0000,"Tags":"","TagsTxt":"","OrderType":0,"InGoodPrice":0,"IsApp":0,"InNoEnsureList":0,"LevelType2":10,"RewardMoney":0,"PriPartner":0,"GoodProportion":18.56}],"RecordCount":29,"HasNew":0}'''

print("=== 服务器算法验证 ===")

# 计算UTF-8字节数
utf8_bytes = json_data.encode('utf-8')
print(f"JSON字符数: {len(json_data)}")
print(f"UTF-8字节数: {len(utf8_bytes)}")

# 与之前验证的对比
print(f"\n=== 与服务器对比 ===")
print(f"之前验证的Content-Length: 10983字节")
print(f"当前JSON UTF-8大小: {len(utf8_bytes)}字节")
print(f"差异: {len(utf8_bytes) - 10983}字节")

if len(utf8_bytes) == 10983:
    print("✅ 完全匹配！这就是对应Content-Length: 10983的JSON")
else:
    print("❌ 不匹配，说明这不是对应Content-Length: 10983的JSON")

# 字符分析
ascii_count = sum(1 for c in json_data if ord(c) < 128)
chinese_count = sum(1 for c in json_data if 0x4E00 <= ord(c) <= 0x9FFF)
special_count = len(json_data) - ascii_count - chinese_count

print(f"\n=== 字符分析 ===")
print(f"ASCII字符: {ascii_count}个")
print(f"中文字符: {chinese_count}个")
print(f"特殊字符: {special_count}个")

# 预测字节数
predicted = ascii_count + chinese_count * 3 + special_count * 2
print(f"预测UTF-8字节数: {predicted}")
print(f"预测准确性: {'✅' if predicted == len(utf8_bytes) else '❌'}")

print(f"\n=== 结论 ===")
print(f"✅ 服务器算法: Content-Length = JSON.toUtf8().length")
print(f"✅ ISIZE验证: 最可靠的gzip完整性检查")
print(f"✅ 不依赖Content-Length进行gzip验证")
