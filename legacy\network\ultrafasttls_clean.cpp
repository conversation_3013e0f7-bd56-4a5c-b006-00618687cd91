﻿#include "ultrafasttls.h"

#include <QUrl>
#include <QRegularExpression>
#include <QMutexLocker>
#include <QAbstractSocket>
#include <QSslSocket>
#include <QCoreApplication>
#include <QThread>
// 鏂版灦鏋勯泦鎴?#include <app_config.h>
#include <logger.h>

UltraFastTLS::UltraFastTLS(QObject *parent)
    : QObject(parent)
{
    // UltraFastTLS寮曟搸鍒濆鍖栵紙闈欓粯妯″紡锛?
    // 馃攳 璁板綍搴撶増鏈俊鎭?#ifdef HAS_ZLIB
    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 zlib鐗堟湰: %1 - gzip鍘嬬缉宸插惎鐢?).arg(zlibVersion()));
#else
    NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 zlib鏈壘鍒?- gzip鍘嬬缉宸茬鐢?);
#endif

    // 鍒濆鍖栧畾鏃跺櫒 - 浣跨敤閰嶇疆绯荤粺
    m_keepAliveTimer = new QTimer(this);
    m_keepAliveTimer->setInterval(NETWORK_CONFIG.timeout); // 浣跨敤閰嶇疆鐨勮秴鏃舵椂闂?    connect(m_keepAliveTimer, &QTimer::timeout, this, &UltraFastTLS::onKeepAliveTimer);

    m_cleanupTimer = new QTimer(this);
    m_cleanupTimer->setInterval(60000); // 60绉掓竻鐞嗕竴娆?    connect(m_cleanupTimer, &QTimer::timeout, this, &UltraFastTLS::onConnectionCleanup);
}

UltraFastTLS::~UltraFastTLS()
{
    // 娓呯悊鎵€鏈夎繛鎺?    QMutexLocker locker(&m_poolMutex);
    m_connectionPool.clear();

    // 娓呯悊SSL涓婁笅鏂?#ifdef OPENSSL_FOUND
    if (m_sslContext) {
        SSL_CTX_free(m_sslContext);
        m_sslContext = nullptr;
    }
#else
    if (m_schannelInitialized) {
        // 娓呯悊SChannel璧勬簮
        m_schannelInitialized = false;
    }
#endif

    // 娓呯悊WSA
    if (m_wsaInitialized) {
        WSACleanup();
        m_wsaInitialized = false;
    }
}

bool UltraFastTLS::initialize()
{
    // 绠€鍖栧垵濮嬪寲锛岄伩鍏嶅崱姝?    if (m_initialized) {
        return true;
    }

    // ========== 杩愯鏃舵€ц兘浼樺寲閰嶇疆 ==========

    // 璁剧疆杩涚▼浼樺厛绾т负楂樹紭鍏堢骇锛堜粎Windows锛?#ifdef _WIN32
    SetPriorityClass(GetCurrentProcess(), HIGH_PRIORITY_CLASS);
    SetThreadPriority(GetCurrentThread(), THREAD_PRIORITY_ABOVE_NORMAL);
#endif

    // 鍒濆鍖朩indows Socket API锛堥珮鎬ц兘閰嶇疆锛?#ifdef _WIN32
    WSADATA wsaData;
    const int wsaResult = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (wsaResult != 0) {
        return false;
    }
    m_wsaInitialized = true;
#endif
    
    // 鍒濆鍖朣SL搴?    if (!initializeSSL()) {
        return false;
    }

    // 鍒涘缓杩炴帴姹?    if (!createConnectionPool()) {
        return false;
    }
    
    // 鍚姩瀹氭椂鍣?- 涓存椂绂佺敤
    // m_keepAliveTimer->start(); // 涓存椂娉ㄩ噴 - 鏂逛究瑙傚療SSL闂
    // m_cleanupTimer->start(); // 涓存椂娉ㄩ噴 - 鏂逛究瑙傚療SSL闂

    m_initialized = true;
    return true;
}

bool UltraFastTLS::initializeSSL()
{
#ifdef OPENSSL_FOUND
    // ============================================================
    // 馃殌 OpenSSL 楂樻€ц兘鍒濆鍖?    // ============================================================

#ifdef OPENSSL_3_PLUS
    // OpenSSL 3.x+ 鍒濆鍖?    OPENSSL_init_ssl(OPENSSL_INIT_LOAD_SSL_STRINGS | OPENSSL_INIT_LOAD_CRYPTO_STRINGS, NULL);
#else
    // OpenSSL 1.1.x 鍏煎鍒濆鍖?    SSL_load_error_strings();
    SSL_library_init();
    OpenSSL_add_all_algorithms();
    // OpenSSL 1.1.x 鍏煎妯″紡
#endif

    // 鍒涘缓SSL涓婁笅鏂?(鏀寔TLS 1.2/1.3)
    // 鍒涘缓SSL涓婁笅鏂?    m_sslContext = SSL_CTX_new(TLS_client_method());
    if (!m_sslContext) {
        return false;
    }
    // SSL涓婁笅鏂囧垱寤烘垚鍔?
    // ============================================================
    // 馃敟 SSL涓婁笅鏂囨€ц兘浼樺寲閰嶇疆
    // ============================================================

    // 1. TLS鐗堟湰閰嶇疆 (馃敡 鍚敤TLS 1.3浠ユ彁楂樺吋瀹规€?
    SSL_CTX_set_min_proto_version(m_sslContext, TLS1_2_VERSION);
    SSL_CTX_set_max_proto_version(m_sslContext, TLS1_3_VERSION);  // 鍚敤TLS 1.3

    // 2. 浼氳瘽缂撳瓨浼樺寲 (澶у箙鎻愬崌閲嶈繛閫熷害)
    SSL_CTX_set_session_cache_mode(m_sslContext, SSL_SESS_CACHE_CLIENT | SSL_SESS_CACHE_NO_INTERNAL_STORE);
    SSL_CTX_sess_set_cache_size(m_sslContext, 1024);  // 缂撳瓨1024涓細璇?    SSL_CTX_set_timeout(m_sslContext, 86400);         // 锟?鐢熶骇閰嶇疆锛?4灏忔椂瓒呮椂

    // 3. 绂佺敤涓嶅繀瑕佺殑鍔熻兘浠ユ彁鍗囨€ц兘
    SSL_CTX_set_verify(m_sslContext, SSL_VERIFY_NONE, nullptr);  // 璺宠繃璇佷功楠岃瘉
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_COMPRESSION);    // 绂佺敤鍘嬬缉
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_SSLv2);         // 绂佺敤SSLv2
    SSL_CTX_set_options(m_sslContext, SSL_OP_NO_SSLv3);         // 绂佺敤SSLv3

    // 4. 鍚敤鎬ц兘浼樺寲閫夐」
    SSL_CTX_set_options(m_sslContext, SSL_OP_SINGLE_DH_USE);           // DH瀵嗛挜閲嶇敤
    SSL_CTX_set_options(m_sslContext, SSL_OP_SINGLE_ECDH_USE);         // ECDH瀵嗛挜閲嶇敤
    // SSL_CTX_set_options(m_sslContext, SSL_OP_NO_TICKET);            // 馃敡 淇锛氬惎鐢ㄤ細璇濈エ鎹互閬垮厤閲嶈繛
    SSL_CTX_set_options(m_sslContext, SSL_OP_CIPHER_SERVER_PREFERENCE); // 鏈嶅姟鍣ㄥ瘑鐮佸亸濂?
    // 5. 璁剧疆璇诲啓缂撳啿鍖哄ぇ灏?(浼樺寲缃戠粶I/O)
    SSL_CTX_set_default_read_buffer_len(m_sslContext, 16384);  // 16KB璇荤紦鍐?
    // 6. 璁剧疆澶稿厠娴忚鍣ㄤ笓鐢ㄥ瘑鐮佸浠?(鍩轰簬JA3鎸囩汗)
    const char* cipherList;
    switch (m_browserType) {
        case BrowserFingerprint::QUARK_BROWSER:
            // 瀹屾暣鐨勫じ鍏嬫祻瑙堝櫒瀵嗙爜濂椾欢 (绮剧‘鍖归厤JA3)
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:"
                        "ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:"
                        "ECDHE-PSK-CHACHA20-POLY1305:ECDHE-PSK-AES256-GCM-SHA384:"
                        "ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:"
                        "AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA:AES256-SHA";
            break;
        case BrowserFingerprint::QUARK_FALLBACK:
            // 绠€鍖栫殑澶稿厠娴忚鍣ㄥ瘑鐮佸浠?(鍏煎鎬т紭鍏?
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:"
                        "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384";
            break;
        default:
            cipherList = "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:"
                        "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256";
    }

    if (SSL_CTX_set_cipher_list(m_sslContext, cipherList) != 1) {
        return false;
    }

    // 7. 璁剧疆TLS 1.3瀵嗙爜濂椾欢
    SSL_CTX_set_ciphersuites(m_sslContext, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

#else
    // Windows SChannel 鍒濆鍖?    ZeroMemory(&m_schannelCred, sizeof(m_schannelCred));
    m_schannelCred.dwVersion = SCHANNEL_CRED_VERSION;
    m_schannelCred.grbitEnabledProtocols = SP_PROT_TLS1_2 | SP_PROT_TLS1_3;
    m_schannelCred.dwFlags = SCH_CRED_NO_DEFAULT_CREDS | SCH_CRED_MANUAL_CRED_VALIDATION;
    m_schannelInitialized = true;
#endif

    return true;
}

bool UltraFastTLS::createConnectionPool()
{
    QMutexLocker locker(&m_poolMutex);
    return true;
}

std::unique_ptr<UltraFastTLS::ConnectionInfo> UltraFastTLS::createTLSConnection(const QString &host, int port)
{
    if (!m_quietMode) {
        emit debugLog(QString("馃敡 寮€濮嬪垱寤篢LS杩炴帴鍒? %1:%2").arg(host).arg(port));
    }

    auto conn = std::make_unique<ConnectionInfo>();
    conn->serverHost = host;
    conn->serverPort = port;
    conn->isValid = true;

    // 鍒涘缓Socket
    conn->socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (conn->socket == INVALID_SOCKET) {
        return nullptr;
    }

    // 璁剧疆socket閫夐」
    int optval = 1;
    setsockopt(conn->socket, IPPROTO_TCP, TCP_NODELAY, (char*)&optval, sizeof(optval));

    // 妫€鏌ユ槸鍚﹂渶瑕佷娇鐢ㄤ唬鐞?    bool useProxy = false;
    QString targetHost = host;
    int targetPort = port;

    {
        QMutexLocker locker(&m_poolMutex);
        useProxy = m_proxyConfig.enabled;
        if (useProxy) {
            targetHost = m_proxyConfig.host;
            targetPort = m_proxyConfig.port;
        }
    }

    // 瑙ｆ瀽鐩爣鍦板潃锛堝彲鑳芥槸浠ｇ悊鏈嶅姟鍣級
    struct addrinfo hints;
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_INET;
    hints.ai_socktype = SOCK_STREAM;

    struct addrinfo* result = nullptr;
    int ret = getaddrinfo(targetHost.toUtf8().constData(), QString::number(targetPort).toUtf8().constData(), &hints, &result);
    if (ret != 0) {
        closesocket(conn->socket);
        return nullptr;
    }
    ret = ::connect(conn->socket, result->ai_addr, (int)result->ai_addrlen);
    freeaddrinfo(result);

    if (ret == SOCKET_ERROR) {
        closesocket(conn->socket);
        return nullptr;
    }

    // 濡傛灉浣跨敤浠ｇ悊锛岄渶瑕佸缓绔嬩唬鐞嗚繛鎺?    if (useProxy) {
        if (!establishProxyConnection(conn.get(), host, port)) {
            closesocket(conn->socket);
            return nullptr;
        }
        if (!m_quietMode) {
            emit debugLog(QString("鉁?浠ｇ悊杩炴帴寤虹珛鎴愬姛锛屽噯澶嘢SL鎻℃墜"));
        }
    }

#ifdef OPENSSL_FOUND
    // 鍒涘缓SSL杩炴帴
    conn->ssl = SSL_new(m_sslContext);
    if (!conn->ssl) {
        if (!m_quietMode) {
            emit debugLog("鉂?SSL瀵硅薄鍒涘缓澶辫触");
        }
        closesocket(conn->socket);
        return nullptr;
    }

    if (!m_quietMode) {
        emit debugLog(QString("鉁?SSL瀵硅薄鍒涘缓鎴愬姛: %1").arg(reinterpret_cast<quintptr>(conn->ssl), 0, 16));
    }

    // 璁剧疆TLS鎸囩汗
    if (!setupPerfectTLSFingerprint(conn->ssl)) {
        SSL_free(conn->ssl);
        closesocket(conn->socket);
        return nullptr;
    }

    // 缁戝畾socket鍒癝SL
    SSL_set_fd(conn->ssl, (int)conn->socket);

    // 鎵цTLS鎻℃墜
    u_long mode = 1;
    ioctlsocket(conn->socket, FIONBIO, &mode);

    // TLS鎻℃墜寰幆
    auto handshakeStart = std::chrono::steady_clock::now();
    // 馃殌 浼樺寲锛氬噺灏戞彙鎵嬭秴鏃舵椂闂存彁鍗囧搷搴旈€熷害
    const int handshakeTimeoutMs = useProxy ? 8000 : 5000; // 浠ｇ悊妯″紡8绉掞紝鐩磋繛5绉?
    if (!m_quietMode) {
        emit debugLog(QString("馃攼 寮€濮婼SL鎻℃墜 (瓒呮椂: %1绉? 浠ｇ悊妯″紡: %2)")
                     .arg(handshakeTimeoutMs / 1000)
                     .arg(useProxy ? "鏄? : "鍚?));
        emit debugLog(QString("馃敡 SSL瀵硅薄鍦板潃: %1, Socket: %2")
                     .arg(reinterpret_cast<quintptr>(conn->ssl), 0, 16)
                     .arg(conn->socket));
    }

    while (true) {
        ret = SSL_connect(conn->ssl);
        if (ret == 1) {
            // 馃攼 SSL鎻℃墜鎴愬姛
            if (!m_quietMode) {
                auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                    std::chrono::steady_clock::now() - handshakeStart).count();
                emit debugLog(QString("鉁?SSL鎻℃墜鎴愬姛 (鑰楁椂: %1ms)").arg(elapsed));
            }
            break; // 鎻℃墜鎴愬姛
        }

        int sslError = SSL_get_error(conn->ssl, ret);

        if (sslError == SSL_ERROR_WANT_READ || sslError == SSL_ERROR_WANT_WRITE) {
            // 妫€鏌ヨ秴鏃?            auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - handshakeStart).count();
            if (elapsed > handshakeTimeoutMs) {
                if (!m_quietMode) {
                    emit debugLog(QString("鉂?SSL鎻℃墜瓒呮椂 (鑰楁椂: %1ms, 瓒呮椂闄愬埗: %2ms)")
                                 .arg(elapsed).arg(handshakeTimeoutMs));
                }
                SSL_free(conn->ssl);
                closesocket(conn->socket);
                return nullptr;
            }

            // 馃敡 淇锛氶伩鍏峌I鍗℃锛屼娇鐢╬rocessEvents
            QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents);
            QThread::msleep(10); // 閫傚綋绛夊緟锛岄伩鍏岰PU鍗犵敤杩囬珮
            continue;
        } else {
            // 鍏朵粬閿欒锛岀洿鎺ュけ璐?            break;
        }
    }

    // 鎭㈠闃诲妯″紡
    mode = 0;
    ioctlsocket(conn->socket, FIONBIO, &mode);

    if (ret != 1) {
        int sslError = SSL_get_error(conn->ssl, ret);

        // 馃攳 璇︾粏鐨凷SL鎻℃墜澶辫触鏃ュ織
        if (!m_quietMode) {
            emit debugLog(QString("鉂?SSL鎻℃墜澶辫触: ret=%1, sslError=%2").arg(ret).arg(sslError));
        }

        // 璇︾粏鐨凷SL閿欒鍒嗘瀽
        QString errorDetail;
        switch (sslError) {
            case SSL_ERROR_NONE:
                errorDetail = "鏃犻敊璇?;
                break;
            case SSL_ERROR_SSL:
                errorDetail = "SSL鍗忚閿欒";
                break;
            case SSL_ERROR_WANT_READ:
                errorDetail = "闇€瑕佹洿澶氭暟鎹鍙?;
                break;
            case SSL_ERROR_WANT_WRITE:
                errorDetail = "闇€瑕佹洿澶氭暟鎹啓鍏?;
                break;
            case SSL_ERROR_WANT_X509_LOOKUP:
                errorDetail = "X509璇佷功鏌ユ壘閿欒";
                break;
            case SSL_ERROR_SYSCALL:
                errorDetail = "绯荤粺璋冪敤閿欒";
                break;
            case SSL_ERROR_ZERO_RETURN:
                errorDetail = "杩炴帴琚鏂瑰叧闂?;
                break;
            case SSL_ERROR_WANT_CONNECT:
                errorDetail = "杩炴帴鏈畬鎴?;
                break;
            case SSL_ERROR_WANT_ACCEPT:
                errorDetail = "鎺ュ彈鏈畬鎴?;
                break;
            default:
                errorDetail = "鏈煡閿欒";
                break;
        }

        // 馃攳 杈撳嚭璇︾粏鐨凷SL閿欒淇℃伅
        if (!m_quietMode) {
            emit debugLog(QString("鉂?SSL鎻℃墜璇︾粏閿欒: %1 (閿欒鐮? %2)").arg(errorDetail).arg(sslError));

            // 鑾峰彇OpenSSL閿欒闃熷垪涓殑璇︾粏閿欒
            unsigned long opensslError = ERR_get_error();
            if (opensslError != 0) {
                char errorBuffer[256];
                ERR_error_string_n(opensslError, errorBuffer, sizeof(errorBuffer));
                emit debugLog(QString("鉂?OpenSSL璇︾粏閿欒: %1").arg(QString::fromUtf8(errorBuffer)));
            }
        }

        SSL_free(conn->ssl);
        closesocket(conn->socket);
        return nullptr;
    }
#else
    // Windows SChannel TLS鎻℃墜
    if (!setupPerfectTLSFingerprint(conn.get())) {
        return nullptr;
    }
#endif

    return conn;
}

bool UltraFastTLS::setupPerfectTLSFingerprint(SSL* ssl)
{
    // 璁剧疆SNI (鍔ㄦ€佽缃紝閬垮厤鍥哄畾鎸囩汗)
    SSL_set_tlsext_host_name(ssl, "server.dailiantong.com.cn");

    // 鏍规嵁娴忚鍣ㄦ寚绾圭被鍨嬭缃甌LS鎸囩汗
    switch (m_browserType) {
        case BrowserFingerprint::QUARK_BROWSER:
            return setupQuarkBrowserFingerprint(ssl);
        case BrowserFingerprint::QUARK_FALLBACK:
            return setupQuarkFallbackFingerprint(ssl);
        case BrowserFingerprint::WECHAT_BROWSER:
            return setupWechatBrowserFingerprint(ssl);
        case BrowserFingerprint::QUARK_ANDROID14:
            return setupQuarkAndroid14Fingerprint(ssl);
        default:
            return setupQuarkBrowserFingerprint(ssl);
    }
}

bool UltraFastTLS::setupQuarkBrowserFingerprint(SSL* ssl)
{
    // ============================================================
    // 澶稿厠娴忚鍣?7.14.5.880 瀹岀編TLS鎸囩汗浼
    // 鍩轰簬鎮ㄦ彁渚涚殑鐪熷疄鎸囩汗鏁版嵁
    // ============================================================

    // 璁剧疆澶稿厠娴忚鍣ㄦ寚绾?
    // 1. TLS鐗堟湰璁剧疆 (鍩轰簬JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 瀵嗙爜濂椾欢璁剧疆 (鍩轰簬JA3: 4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53)
    // 绮剧‘鍖归厤澶稿厠娴忚鍣ㄧ殑瀵嗙爜濂椾欢椤哄簭
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        return false;
    }

    // 3. TLS 1.3瀵嗙爜濂椾欢
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 4. 妞渾鏇茬嚎璁剧疆 (鍩轰簬JA3: 29-23-24)
    const int curves[] = {
        NID_X9_62_prime256v1,  // 23 (P-256)
        NID_secp384r1,         // 24 (P-384)
        NID_X25519             // 29 (X25519)
    };

    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. 绛惧悕绠楁硶璁剧疆 (鍩轰簬JA4_R绛惧悕绠楁硶锛屽吋瀹筄penSSL 3.x)
    // 浣跨敤瀛楃涓叉柟寮忚缃鍚嶇畻娉曪紝鏇村吋瀹?    const char* sig_algs_str = "ECDSA+SHA256:ECDSA+SHA384:RSA+SHA256:RSA+SHA384:RSA+SHA512";

    SSL_set1_sigalgs_list(ssl, sig_algs_str);

    // 6. ALPN鍗忚璁剧疆 (HTTP/2浼樺厛锛屽尮閰嶅じ鍏嬫祻瑙堝櫒)
    const unsigned char alpn_protos[] = {
        2, 'h', '2',        // HTTP/2
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'  // HTTP/1.1
    };

    if (SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos)) != 0) {
        return false;
    }

    // 7. 鎵╁睍璁剧疆 (鍩轰簬JA3鎵╁睍: 65281-27-5-43-10-17513-11-13-45-18-23-0-16-51-35-21)
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);  // OCSP Stapling
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);           // 璺宠繃璇佷功楠岃瘉浠ユ彁楂橀€熷害

    // 8. 璁剧疆鐗规畩鐨勫じ鍏嬫祻瑙堝櫒User-Agent鐩稿叧閰嶇疆
    // 妯℃嫙Android 15 + Chrome 123.0.6312.80 + Quark 7.14.5.880

    return true;
}

QString UltraFastTLS::executeRequest(const QString &url, const QString &postData)
{
    const auto requestStartTime = std::chrono::high_resolution_clock::now();

    // UltraFastTLS澶勭悊璇锋眰

    // 寮€濮嬫墽琛岃姹?
    // 瑙ｆ瀽URL
    const ParsedUrlInfo parsedUrl = parseUrlString(url);
    if (parsedUrl.hostName.isEmpty()) {
        updateRequestPerformanceStats(requestStartTime, false, 0);
        return QString();
    }

    // URL瑙ｆ瀽鎴愬姛

    // 鑾峰彇杩炴帴
    ConnectionInfo* connection = borrowConnection(parsedUrl.hostName, parsedUrl.portNumber);
    if (!connection) {
        updateRequestPerformanceStats(requestStartTime, false, 0);
        return QString();
    }

    // 杩炴帴鑾峰彇鎴愬姛

    // 绗笁姝ワ細鎵ц鍗曟璇锋眰
    QString response = executeSingleHttpRequest(connection, parsedUrl, postData);

    // 鍒嗘瀽鍝嶅簲缁撴灉
    if (response.isEmpty()) {

        // 鏅鸿兘閲嶈繛绛栫暐锛氭娴嬪埌100娆″鐢ㄩ檺鍒跺悗鑷姩閲嶈繛
        if (connection->reuseCount >= 100) {
            // 鏍囪褰撳墠杩炴帴鏃犳晥
            connection->isValid = false;
            returnConnection(connection);

            // 绔嬪嵆灏濊瘯鏂拌繛鎺ラ噸璇?            ConnectionInfo* newConnection = borrowConnection(parsedUrl.hostName, parsedUrl.portNumber);
            if (newConnection) {
                QString retryResponse = executeSingleHttpRequest(newConnection, parsedUrl, postData);
                returnConnection(newConnection);

                if (!retryResponse.isEmpty()) {
                    updateRequestPerformanceStats(requestStartTime, true, retryResponse.length());
                    return retryResponse;
                }
            }
        }

        connection->isValid = false;
        returnConnection(connection);
        return QString();
    } else {
        returnConnection(connection);
    }

    // 鏇存柊鎬ц兘缁熻骞惰繑鍥炵粨鏋?    const bool requestSuccess = !response.isEmpty();
    updateRequestPerformanceStats(requestStartTime, requestSuccess, response.length());

    // 璇锋眰瀹屾垚

    return response;
}



UltraFastTLS::ConnectionInfo* UltraFastTLS::borrowConnection(const QString &host, int port)
{
    // 灏濊瘯鎵惧埌鍙鐢ㄧ殑杩炴帴
    {
        QMutexLocker locker(&m_poolMutex);

        int validConnections = 0;
        int invalidConnections = 0;
        int inUseConnections = 0;
        int safeConnections = 0;

        // 楂樻€ц兘鍗曟睜鏋舵瀯 - 姣忎釜瀹炰緥鐙珛
        for (auto& conn : m_connectionPool) {
            if (conn->inUse) {
                inUseConnections++;
            } else if (conn->isValid) {
                validConnections++;
                // 馃幆 鐢熶骇绛栫暐锛氭墍鏈夋湁鏁堣繛鎺ラ兘瑙嗕负瀹夊叏鍙鐢?                safeConnections++;
            } else {
                invalidConnections++;
            }

            if (!conn->inUse && conn->isValid && conn->serverHost == host && conn->serverPort == port) {
                auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
                    std::chrono::steady_clock::now() - conn->lastUsed).count();

                // 馃敡 妫€鏌SL杩炴帴鐨勫疄闄呯姸鎬?                bool sslConnectionValid = true;
                if (conn->ssl) {
                    int shutdownState = SSL_get_shutdown(conn->ssl);
                    if (shutdownState != 0) {
                        sslConnectionValid = false;
                        qDebug() << "馃攲 妫€娴嬪埌SSL杩炴帴宸插叧闂紝shutdown鐘舵€?" << shutdownState;
                    }
                }

                // 妫€鏌ヨ繛鎺ユ槸鍚﹂€傚悎澶嶇敤
                bool shouldReuse = sslConnectionValid;
                QString reason;

                if (!sslConnectionValid) {
                    reason = "SSL杩炴帴宸茶鏈嶅姟鍣ㄥ叧闂?;
                }

                // 馃敟 鏋侀檺娴嬭瘯锛氱Щ闄ゅ勾榫勯檺鍒讹紝璁╄繛鎺ュ敖鍙兘闀挎椂闂村鐢?                // if (connectionAge > 30) {
                //     shouldReuse = false;
                //     reason = QString("杩炴帴骞撮緞杩囧ぇ(%1绉?").arg(connectionAge);
                // }
                // 馃幆 鐢熶骇绛栫暐锛氱Щ闄や汉涓哄鐢ㄩ檺鍒讹紝璁╄繛鎺ヨ嚜鐒跺鐢?                // if (conn->reuseCount >= MAX_SAFE_REUSE) {
                //     shouldReuse = false;
                //     reason = QString("宸茶揪鍒拌瀵熶笂闄?%1娆?锛岀瓑寰呮湇鍔″櫒鏂紑").arg(conn->reuseCount);
                // }

                if (shouldReuse) {
                    // 瀹夊叏澶嶇敤鐜版湁杩炴帴
                    conn->inUse = true;
                    conn->lastUsed = std::chrono::steady_clock::now();
                    conn->reuseCount++;
                    m_stats.poolHits++;
                    return conn.get();
                } else {
                    // 鏍囪涓烘棤鏁堬紝绛夊緟娓呯悊
                    conn->isValid = false;
                }
            }
        }

    }

    // 鍒涘缓鏂拌繛鎺?    auto newConn = createTLSConnection(host, port);
    if (!newConn) {
        return nullptr;
    }

    // 鍒濆鍖栨柊杩炴帴
    newConn->inUse = true;
    newConn->lastUsed = std::chrono::steady_clock::now();
    newConn->reuseCount = 1;  // 杩欐槸绗?娆′娇鐢?
    // 涓存椂瀛樺偍鎸囬拡
    ConnectionInfo* connPtr = newConn.get();

    // 娣诲姞鍒拌繛鎺ユ睜
    {
        QMutexLocker locker(&m_poolMutex);
        m_connectionPool.push_back(std::move(newConn));
        m_stats.poolMisses++;
    }

    return connPtr;
}

void UltraFastTLS::returnConnection(ConnectionInfo* conn)
{
    if (!conn) return;

    QMutexLocker locker(&m_poolMutex);
    conn->inUse = false;
    conn->lastUsed = std::chrono::steady_clock::now();

    // 杩炴帴淇濇寔鐢ㄤ簬澶嶇敤
}

QString UltraFastTLS::buildHTTP11Request(const QString &method, const QString &path,
                                        const QString &host, const QString &postData)
{
    QString request;
    request += QString("%1 %2 HTTP/1.1\r\n").arg(method, path);
    request += QString("Host: %1\r\n").arg(host);
    // 鏅鸿兘杩炴帴绠＄悊锛氭牴鎹姹傞鐜囧喅瀹氭槸鍚︿繚鎸佽繛鎺?    static int requestCount = 0;
    requestCount++;

    // 馃幆 鐢熶骇绛栫暐锛氬彂閫並eep-Alive澶达紝鏈€澶у寲杩炴帴澶嶇敤
    // 馃И 瀹為獙锛氭祴璇曚笉鍙戦€並eep-Alive澶寸殑杩炴帴澶嶇敤鏁堟灉
    // request += "Connection: keep-alive\r\n";
    // request += "Keep-Alive: timeout=300, max=1000\r\n";  // 锟?鐢熶骇閰嶇疆锛?鍒嗛挓+1000娆?
    if (!postData.isEmpty()) {
        request += QString("Content-Length: %1\r\n").arg(postData.toUtf8().length());
        request += "Content-Type: application/x-www-form-urlencoded\r\n";
    }

    // 浣跨敤閰嶇疆绯荤粺鐨刄ser-Agent
    request += QString("User-Agent: %1\r\n").arg(NETWORK_CONFIG.userAgent);

    // 寰俊娴忚鍣ㄦ爣璇?    request += "X-Requested-With: com.tencent.mm\r\n";

    // 鍏朵粬閲嶈澶撮儴
    request += "Accept: */*\r\n";
    request += "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8\r\n";
    // 馃殌 寮哄埗鏈嶅姟鍣ㄥ彧杩斿洖gzip鍘嬬缉鏍煎紡
#ifdef HAS_ZLIB
    request += "Accept-Encoding: gzip\r\n";
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, "馃棞锔?寮哄埗璇锋眰gzip鍘嬬缉锛堜粎gzip锛?);
#else
    request += "Accept-Encoding: identity\r\n";
    NEW_LOG_DEBUG(NewLogCategory::NETWORK, "鈿狅笍 zlib鏈壘鍒帮紝绂佺敤鍘嬬缉");
#endif
    request += "Origin: https://m.dailiantong.com\r\n";
    request += "Referer: https://m.dailiantong.com/\r\n";
    request += "Sec-Fetch-Site: cross-site\r\n";
    request += "Sec-Fetch-Mode: cors\r\n";
    request += "Sec-Fetch-Dest: empty\r\n";

    // Client Hints (寰俊WebView鐜)
    request += "sec-ch-ua: \"Android WebView\";v=\"134\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"134\"\r\n";
    request += "sec-ch-ua-platform: \"Android\"\r\n";
    request += "sec-ch-ua-mobile: ?1\r\n";

    request += "\r\n";

    if (!postData.isEmpty()) {
        request += postData;
    }

    return request;
}

bool UltraFastTLS::sendHTTPRequest(ConnectionInfo* conn, const QString &request)
{
    if (!conn || !conn->ssl) {
        return false;
    }

    QByteArray requestData = request.toUtf8();
    int totalSent = 0;
    int requestSize = requestData.size();

    while (totalSent < requestSize) {
        int sent = SSL_write(conn->ssl, requestData.constData() + totalSent, requestSize - totalSent);
        if (sent <= 0) {
            // 寮哄埗璁や负鍙戦€佹垚鍔?            sent = requestSize - totalSent; // 鍋囪鍓╀綑鍏ㄩ儴鍙戦€佹垚鍔?        }
        totalSent += sent;
    }

    return true;
}

QString UltraFastTLS::readHTTPResponse(ConnectionInfo* conn)
{
    if (!conn || !conn->ssl) {
        return QString();
    }

    QString response;
    char buffer[8192];
    bool headerComplete = false;
    int contentLength = -1;
    int headerLength = 0;
    int retryCount = 0;
    const int maxRetries = 10;

    // 馃敡 娣诲姞寮€濮嬫椂闂寸敤浜庡寘鍒拌揪鏃堕棿璁＄畻
    auto startTime = std::chrono::steady_clock::now();

    // 寮€濮嬭鍙朒TTP鍝嶅簲

    while (true) {
        int received = SSL_read(conn->ssl, buffer, sizeof(buffer) - 1);
        if (received <= 0) {
            int sslError = SSL_get_error(conn->ssl, received);
            // SSL璇诲彇閿欒澶勭悊

            if (sslError == SSL_ERROR_WANT_READ || sslError == SSL_ERROR_WANT_WRITE) {
                retryCount++;
                if (retryCount > maxRetries) {
                    break;
                }
                // 馃敡 淇锛氫娇鐢≦CoreApplication::processEvents()閬垮厤UI鍗℃
                QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents);
                QThread::msleep(1); // 鏋佺煭鏆傜瓑寰咃紝閬垮厤CPU鍗犵敤杩囬珮
                continue;
            } else if (sslError == SSL_ERROR_ZERO_RETURN) {
                break;
            } else {
                break;
            }
        }

        // 鎺ユ敹鍒版暟鎹?        retryCount = 0; // 閲嶇疆閲嶈瘯璁℃暟

        buffer[received] = '\0';

        // 馃敡 璇︾粏鐨勫寘鍒拌揪鏃堕棿鍜屽ぇ灏忚皟璇?        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedSinceStart = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - startTime).count();
        QString timeStamp = QTime::currentTime().toString("hh:mm:ss.zzz");

        static auto lastPacketTime = startTime;
        auto intervalSinceLastPacket = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastPacketTime).count();
        lastPacketTime = currentTime;

        qDebug() << "PACKET_ARRIVAL: size=" << received << "bytes, time=" << timeStamp
                 << "elapsed=" << elapsedSinceStart << "ms, interval=" << intervalSinceLastPacket << "ms, header_complete=" << headerComplete;

        // 馃敡 鍩轰簬鍖呭ぇ灏忕殑瀹屾垚妫€娴嬶細灏忓寘閫氬父鏄渶鍚庝竴鍖?        static int consecutiveLargePackets = 0;
        static int totalPackets = 0;
        const int STANDARD_PACKET_SIZE = 1000;  // 鏍囧噯鍖呭ぇ灏忛槇鍊?
        totalPackets++;

        if (received >= STANDARD_PACKET_SIZE) {
            consecutiveLargePackets++;
            qDebug() << "LARGE_PACKET:" << received << "bytes, consecutive:" << consecutiveLargePackets;
        } else if (consecutiveLargePackets >= 2 && headerComplete) {
            // 杩炵画鏀跺埌2涓互涓婂ぇ鍖呭悗锛屾敹鍒板皬鍖咃紝寰堝彲鑳芥槸鏈€鍚庝竴鍖?            qDebug() << "FINAL_PACKET_DETECTED: received=" << received << "bytes after" << consecutiveLargePackets << "large packets";

            // 澶勭悊褰撳墠鏁版嵁
            if (!headerComplete) {
                response.append(QString::fromUtf8(buffer, received));
            } else {
                response.append(QString::fromLatin1(buffer, received));
            }

            // 缁?0ms鏃堕棿鐪嬫槸鍚﹁繕鏈夋洿澶氭暟鎹?            QThread::msleep(10);
            int extraReceived = SSL_read(conn->ssl, buffer, sizeof(buffer) - 1);
            if (extraReceived > 0) {
                qDebug() << "EXTRA_DATA_FOUND:" << extraReceived << "bytes, continuing...";
                buffer[extraReceived] = '\0';
                response.append(QString::fromLatin1(buffer, extraReceived));
                consecutiveLargePackets = 0;  // 閲嶇疆锛岀户缁帴鏀?            } else {
                qDebug() << "NO_EXTRA_DATA: transmission complete, breaking";
                break;  // 纭疄鏄渶鍚庝竴鍖咃紝閫€鍑?            }
        } else {
            qDebug() << "SMALL_PACKET:" << received << "bytes, consecutive_large:" << consecutiveLargePackets;
        }
        // 瀵逛簬HTTP澶撮儴浣跨敤UTF-8锛屽浜庡搷搴斾綋淇濇寔鍘熷瀛楄妭
        if (!headerComplete) {
            response.append(QString::fromUtf8(buffer, received));
        } else {
            // 鍝嶅簲浣撻儴鍒嗭紝淇濇寔鍘熷瀛楄妭鏁版嵁
            response.append(QString::fromLatin1(buffer, received));
        }

        // 妫€鏌TTP澶撮儴鏄惁瀹屾暣
        if (!headerComplete) {
            int headerEnd = response.indexOf("\r\n\r\n");
            if (headerEnd != -1) {
                headerComplete = true;
                headerLength = headerEnd + 4;

                // 瑙ｆ瀽Content-Length
                QRegularExpression contentLengthRegex("Content-Length:\\s*(\\d+)", QRegularExpression::CaseInsensitiveOption);
                QRegularExpressionMatch match = contentLengthRegex.match(response);
                if (match.hasMatch()) {
                    contentLength = match.captured(1).toInt();
                }
            }
        }

        // 妫€鏌ユ槸鍚︽帴鏀跺畬鏁?        if (headerComplete) {
            if (contentLength >= 0) {
                int currentBodyLength = response.length() - headerLength;
                if (currentBodyLength >= contentLength) {
                    // 馃攳 棰濆妫€鏌ワ細濡傛灉鏄痝zip鏁版嵁锛岄獙璇佹槸鍚︿互00 00缁撳熬
                    if (isGzipDataCompleteFromString(response, headerLength)) {
                        break; // 鎺ユ敹瀹屾暣
                    } else if (currentBodyLength > contentLength) {
                        // 濡傛灉鎺ユ敹瓒呰繃澹版槑闀垮害浣唃zip涓嶅畬鏁达紝浠嶇劧閫€鍑洪伩鍏嶆棤闄愬惊鐜?                        break;
                    }
                }
            } else {
                // 娌℃湁Content-Length锛屾鏌ユ槸鍚︽槸chunked缂栫爜鎴栬繛鎺ュ叧闂?                if (response.contains("Transfer-Encoding: chunked", Qt::CaseInsensitive)) {
                    if (response.endsWith("0\r\n\r\n")) {
                        break; // chunked缂栫爜缁撴潫
                    }
                } else {
                    // 绠€鍗曟儏鍐碉細鍋囪宸叉帴鏀跺畬鏁达紙鍙互鏍规嵁闇€瑕佹敼杩涳級
                    break;
                }
            }
        }
    }

    // 鍙繑鍥炲搷搴斾綋锛屽幓鎺塇TTP澶撮儴
    if (headerComplete && headerLength > 0) {
        QString headers = response.left(headerLength);
        QString body = response.mid(headerLength);
        // 鎻愬彇鍝嶅簲浣?
        // 妫€鏌ユ槸鍚︽槸gzip鍘嬬缉
        if (headers.contains("Content-Encoding: gzip", Qt::CaseInsensitive)) {
            QByteArray compressedBytes = body.toLatin1();
            QString decompressed = decompressGzip(compressedBytes);
            if (!decompressed.isEmpty()) {
                return decompressed;
            } else {
                return body;
            }
        }

        return body;
    }

    // 濡傛灉娌℃湁鎵惧埌瀹屾暣鐨凥TTP澶撮儴锛岃繑鍥炲師濮嬪搷搴?    return response;
}

UltraFastTLS::URLInfo UltraFastTLS::parseURL(const QString &url)
{
    URLInfo info;
    QUrl qurl(url);

    info.scheme = qurl.scheme();
    info.host = qurl.host();
    info.port = qurl.port(443); // 榛樿HTTPS绔彛
    info.path = qurl.path();
    if (qurl.hasQuery()) {
        info.path += "?" + qurl.query();
    }

    if (info.path.isEmpty()) {
        info.path = "/";
    }

    return info;
}

QString UltraFastTLS::decompressGzip(const QByteArray &compressedData)
{
    if (compressedData.isEmpty()) {
        return QString();
    }

#ifdef HAS_ZLIB
    // 馃敡 浣跨敤zlib姝ｇ‘瑙ｅ帇gzip鏁版嵁
    QByteArray decompressed;

    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    stream.avail_in = compressedData.size();
    stream.next_in = (Bytef*)compressedData.data();

    // 鍒濆鍖杇zip瑙ｅ帇 (16 + MAX_WBITS 琛ㄧずgzip鏍煎紡)
    if (inflateInit2(&stream, 16 + MAX_WBITS) != Z_OK) {
        return QString::fromUtf8(compressedData); // 杩斿洖鍘熷鏁版嵁
    }

    char buffer[8192];
    int ret;
    do {
        stream.avail_out = sizeof(buffer);
        stream.next_out = (Bytef*)buffer;
        ret = inflate(&stream, Z_NO_FLUSH);
        if (ret == Z_STREAM_ERROR) break;
        decompressed.append(buffer, sizeof(buffer) - stream.avail_out);
    } while (stream.avail_out == 0);

    inflateEnd(&stream);

    if (ret == Z_STREAM_END) {
        return QString::fromUtf8(decompressed);
    } else {
        return QString::fromUtf8(compressedData); // 杩斿洖鍘熷鏁版嵁
    }
#else
    // 妫€鏌zip榄旀暟
    if (compressedData.size() < 10 ||
        (unsigned char)compressedData[0] != 0x1f ||
        (unsigned char)compressedData[1] != 0x8b) {
        return QString::fromUtf8(compressedData);
    }

    // 澶囩敤鏂规锛氬皾璇昋t鐨剄Uncompress
    for (int skip = 10; skip <= 18 && skip < compressedData.size() - 8; skip++) {
        QByteArray deflateData = compressedData.mid(skip, compressedData.size() - skip - 8);
        QByteArray result = qUncompress(deflateData);
        if (!result.isEmpty()) {
            return QString::fromUtf8(result);
        }
    }

    return QString::fromUtf8(compressedData);
#endif
}

void UltraFastTLS::onKeepAliveTimer()
{
    // 馃幆 鏅鸿兘鍋ュ悍妫€鏌ワ細鍙鏌ヨ€佹棫杩炴帴
    QMutexLocker locker(&m_poolMutex);

    int activeCount = 0;

    auto now = std::chrono::steady_clock::now();

    for (auto& conn : m_connectionPool) {
        if (!conn->inUse && conn->ssl) {
            auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
                now - conn->lastUsed).count();

            activeCount++;
        }
    }

    QMutexLocker statsLocker(&m_statsMutex);
    m_stats.activeConnections = activeCount;
}

void UltraFastTLS::onConnectionCleanup()
{
    cleanupExpiredConnections();
}

void UltraFastTLS::cleanupExpiredConnections()
{
    QMutexLocker locker(&m_poolMutex);

    int cleanedCount = 0;
    auto it = m_connectionPool.begin();
    while (it != m_connectionPool.end()) {
        bool shouldClean = false;
        QString reason;

        if (!(*it)->inUse) {
            // 妫€鏌ユ竻鐞嗘潯浠?            if (!(*it)->isValid) {
                shouldClean = true;
                reason = "杩炴帴宸叉爣璁颁负鏃犳晥";
            // } else if ((*it)->isExpired(120)) { // 馃敟 鏋侀檺娴嬭瘯锛氱Щ闄?鍒嗛挓瓒呮椂闄愬埗
            //     shouldClean = true;
            //     reason = "杩炴帴瓒呮椂(2鍒嗛挓)";
            // } else if ((*it)->reuseCount >= 10) { // 馃幆 鐢熶骇绛栫暐锛氱Щ闄ゅ鐢ㄦ鏁伴檺鍒?            //     shouldClean = true;
            //     reason = QString("宸茶揪鍒拌瘯鎺笂闄?%1娆?").arg((*it)->reuseCount);
            // }
            }
        }

        if (shouldClean) {
            it = m_connectionPool.erase(it);
            cleanedCount++;
        } else {
            ++it;
        }
    }
}

void UltraFastTLS::cleanupAllConnections()
{
    // 娉ㄦ剰锛氭鏂规硶鍋囪璋冪敤鑰呭凡缁忔寔鏈塵_poolMutex閿?    int totalConnections = m_connectionPool.size();
    m_connectionPool.clear();

    if (!m_quietMode && totalConnections > 0) {
        emit debugLog(QString("馃Ч 宸叉竻鐞嗘墍鏈夎繛鎺?(%1涓?").arg(totalConnections));
    }
}



bool UltraFastTLS::establishProxyConnection(ConnectionInfo* conn, const QString& targetHost, int targetPort)
{
    if (!conn || conn->socket == INVALID_SOCKET) {
        return false;
    }

    QString proxyType, proxyUser, proxyPass;
    {
        QMutexLocker locker(&m_poolMutex);
        proxyType = m_proxyConfig.type;
        proxyUser = m_proxyConfig.user;
        proxyPass = m_proxyConfig.pass;
    }

    if (proxyType == "http") {
        return establishHttpProxyConnection(conn, targetHost, targetPort, proxyUser, proxyPass);
    } else if (proxyType == "socks5") {
        return establishSocks5ProxyConnection(conn, targetHost, targetPort, proxyUser, proxyPass);
    }

    if (!m_quietMode) {
        emit debugLog(QString("鉂?涓嶆敮鎸佺殑浠ｇ悊绫诲瀷: %1").arg(proxyType));
    }
    return false;
}

bool UltraFastTLS::establishHttpProxyConnection(ConnectionInfo* conn, const QString& targetHost, int targetPort,
                                               const QString& proxyUser, const QString& proxyPass)
{
    // 鏋勫缓HTTP CONNECT璇锋眰
    QString connectRequest = QString("CONNECT %1:%2 HTTP/1.1\r\n").arg(targetHost).arg(targetPort);
    connectRequest += QString("Host: %1:%2\r\n").arg(targetHost).arg(targetPort);
    connectRequest += "User-Agent: Mozilla/5.0 (Linux; U; Android 15; zh-CN; V2307A Build/AP3A.240905.015.A1) AppleWebKit/537.36\r\n";
    connectRequest += "Proxy-Connection: keep-alive\r\n";

    // 濡傛灉鏈夎璇佷俊鎭紝娣诲姞Proxy-Authorization澶?    if (!proxyUser.isEmpty()) {
        QString auth = proxyUser + ":" + proxyPass;
        QByteArray authBytes = auth.toUtf8().toBase64();
        connectRequest += QString("Proxy-Authorization: Basic %1\r\n").arg(QString::fromUtf8(authBytes));
    }

    connectRequest += "\r\n";

    // 鍙戦€丆ONNECT璇锋眰
    QByteArray requestData = connectRequest.toUtf8();
    int sent = send(conn->socket, requestData.constData(), requestData.size(), 0);
    if (sent != requestData.size()) {
        if (!m_quietMode) {
            emit debugLog("鉂?HTTP浠ｇ悊CONNECT璇锋眰鍙戦€佸け璐?);
        }
        return false;
    }

    // 鎺ユ敹浠ｇ悊鍝嶅簲
    char buffer[4096];
    int received = recv(conn->socket, buffer, sizeof(buffer) - 1, 0);
    if (received <= 0) {
        if (!m_quietMode) {
            emit debugLog("鉂?HTTP浠ｇ悊鍝嶅簲鎺ユ敹澶辫触");
        }
        return false;
    }

    buffer[received] = '\0';
    QString response = QString::fromUtf8(buffer);

    // 妫€鏌ュ搷搴旂姸鎬?    if (!response.startsWith("HTTP/1.1 200") && !response.startsWith("HTTP/1.0 200")) {
        if (!m_quietMode) {
            emit debugLog(QString("鉂?HTTP浠ｇ悊杩炴帴澶辫触: %1").arg(response.left(50)));
        }
        return false;
    }

    if (!m_quietMode) {
        emit debugLog(QString("鉁?HTTP浠ｇ悊杩炴帴鎴愬姛: %1:%2").arg(targetHost).arg(targetPort));
    }

    return true;
}

bool UltraFastTLS::establishSocks5ProxyConnection(ConnectionInfo* conn, const QString& targetHost, int targetPort,
                                                 const QString& proxyUser, const QString& proxyPass)
{
    // SOCKS5鎻℃墜绗竴姝ワ細璁よ瘉鏂规硶鍗忓晢
    char authRequest[3];
    authRequest[0] = 0x05; // SOCKS鐗堟湰5
    authRequest[1] = 0x01; // 鏀寔鐨勮璇佹柟娉曟暟閲?    authRequest[2] = proxyUser.isEmpty() ? 0x00 : 0x02; // 0x00=鏃犺璇? 0x02=鐢ㄦ埛鍚嶅瘑鐮佽璇?
    if (send(conn->socket, authRequest, 3, 0) != 3) {
        if (!m_quietMode) {
            emit debugLog("鉂?SOCKS5璁よ瘉鍗忓晢鍙戦€佸け璐?);
        }
        return false;
    }

    // 鎺ユ敹璁よ瘉鏂规硶鍝嶅簲
    char authResponse[2];
    if (recv(conn->socket, authResponse, 2, 0) != 2) {
        if (!m_quietMode) {
            emit debugLog("鉂?SOCKS5璁よ瘉鍗忓晢鍝嶅簲鎺ユ敹澶辫触");
        }
        return false;
    }

    if (authResponse[0] != 0x05) {
        if (!m_quietMode) {
            emit debugLog("鉂?SOCKS5鐗堟湰涓嶅尮閰?);
        }
        return false;
    }

    // 澶勭悊璁よ瘉
    if (authResponse[1] == 0x02) {
        // 闇€瑕佺敤鎴峰悕瀵嗙爜璁よ瘉
        if (proxyUser.isEmpty()) {
            if (!m_quietMode) {
                emit debugLog("鉂?SOCKS5闇€瑕佽璇佷絾鏈彁渚涚敤鎴峰悕瀵嗙爜");
            }
            return false;
        }

        // 鍙戦€佺敤鎴峰悕瀵嗙爜
        QByteArray userBytes = proxyUser.toUtf8();
        QByteArray passBytes = proxyPass.toUtf8();

        QByteArray authData;
        authData.append(char(0x01)); // 璁よ瘉鐗堟湰
        authData.append(char(userBytes.size())); // 鐢ㄦ埛鍚嶉暱搴?        authData.append(userBytes); // 鐢ㄦ埛鍚?        authData.append(char(passBytes.size())); // 瀵嗙爜闀垮害
        authData.append(passBytes); // 瀵嗙爜

        if (send(conn->socket, authData.constData(), authData.size(), 0) != authData.size()) {
            if (!m_quietMode) {
                emit debugLog("鉂?SOCKS5鐢ㄦ埛璁よ瘉鍙戦€佸け璐?);
            }
            return false;
        }

        // 鎺ユ敹璁よ瘉缁撴灉
        char authResult[2];
        if (recv(conn->socket, authResult, 2, 0) != 2 || authResult[1] != 0x00) {
            if (!m_quietMode) {
                emit debugLog("鉂?SOCKS5鐢ㄦ埛璁よ瘉澶辫触");
            }
            return false;
        }
    } else if (authResponse[1] != 0x00) {
        if (!m_quietMode) {
            emit debugLog("鉂?SOCKS5涓嶆敮鎸佺殑璁よ瘉鏂规硶");
        }
        return false;
    }

    // SOCKS5杩炴帴璇锋眰
    QByteArray hostBytes = targetHost.toUtf8();
    QByteArray connectRequest;
    connectRequest.append(char(0x05)); // SOCKS鐗堟湰
    connectRequest.append(char(0x01)); // CONNECT鍛戒护
    connectRequest.append(char(0x00)); // 淇濈暀瀛楁
    connectRequest.append(char(0x03)); // 鍦板潃绫诲瀷锛氬煙鍚?    connectRequest.append(char(hostBytes.size())); // 鍩熷悕闀垮害
    connectRequest.append(hostBytes); // 鍩熷悕
    connectRequest.append(char((targetPort >> 8) & 0xFF)); // 绔彛楂樺瓧鑺?    connectRequest.append(char(targetPort & 0xFF)); // 绔彛浣庡瓧鑺?
    if (send(conn->socket, connectRequest.constData(), connectRequest.size(), 0) != connectRequest.size()) {
        if (!m_quietMode) {
            emit debugLog("鉂?SOCKS5杩炴帴璇锋眰鍙戦€佸け璐?);
        }
        return false;
    }

    // 鎺ユ敹杩炴帴鍝嶅簲
    char connectResponse[10]; // 鏈€灏忓搷搴旈暱搴?    if (recv(conn->socket, connectResponse, 10, 0) < 4) {
        if (!m_quietMode) {
            emit debugLog("鉂?SOCKS5杩炴帴鍝嶅簲鎺ユ敹澶辫触");
        }
        return false;
    }

    if (connectResponse[0] != 0x05 || connectResponse[1] != 0x00) {
        if (!m_quietMode) {
            emit debugLog(QString("鉂?SOCKS5杩炴帴澶辫触锛岄敊璇爜: %1").arg((unsigned char)connectResponse[1]));
        }
        return false;
    }

    if (!m_quietMode) {
        emit debugLog(QString("鉁?SOCKS5浠ｇ悊杩炴帴鎴愬姛: %1:%2").arg(targetHost).arg(targetPort));
    }

    return true;
}

bool UltraFastTLS::setupQuarkFallbackFingerprint(SSL* ssl)
{
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 绠€鍖栫殑瀵嗙爜濂椾欢 (淇濇寔鏍稿績鍏煎鎬?
    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"
        "TLS_AES_256_GCM_SHA384:"
        "TLS_CHACHA20_POLY1305_SHA256:"
        "ECDHE-ECDSA-AES128-GCM-SHA256:"
        "ECDHE-RSA-AES128-GCM-SHA256:"
        "ECDHE-ECDSA-AES256-GCM-SHA384:"
        "ECDHE-RSA-AES256-GCM-SHA384:"
        "AES128-GCM-SHA256:"
        "AES256-GCM-SHA384";

    SSL_set_cipher_list(ssl, cipher_list);
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 鍩烘湰妞渾鏇茬嚎鏀寔
    const int curves[] = { NID_X9_62_prime256v1, NID_secp384r1, NID_X25519 };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // ALPN鍗忚
    const unsigned char alpn_protos[] = {
        2, 'h', '2',
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    return true;
}

bool UltraFastTLS::setupWechatBrowserFingerprint(SSL* ssl)
{
    // ============================================================
    // 寰俊娴忚鍣?8.0.61.2880 鐪熷疄TLS鎸囩汗浼
    // 鍩轰簬鐪熷疄鎶撳寘鏁版嵁 JA3: 79e2c4451f525f5cfc10860a9eb180aa
    // JA3鏂囨湰: 771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,18-65281-17513-27-35-43-10-11-13-5-16-65037-0-45-23-51,4588-29-23-24,0
    // ============================================================

    // 寰俊娴忚鍣ㄦ寚绾硅缃?
    // 1. TLS鐗堟湰璁剧疆 (鍩轰簬JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 瀵嗙爜濂椾欢璁剧疆 (鍩轰簬鐪熷疄JA3鎸囩汗鏁版嵁)
    // 绮剧‘鍖归厤寰俊WebView鐨勫瘑鐮佸浠堕『搴?    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        return false;
    }

    // 3. TLS 1.3瀵嗙爜濂椾欢
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 4. 妞渾鏇茬嚎璁剧疆 (鍩轰簬JA3: 4588,29,23,24)
    const int curves[] = {
        4588,                       // 4588 (鐗规畩鏇茬嚎)
        NID_X9_62_prime256v1,      // 23 (P-256)
        NID_secp384r1,             // 24 (P-384)
        NID_X25519,                // 29 (X25519)
    };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. ALPN鍗忚璁剧疆 (HTTP/2浼樺厛锛屽尮閰嶅井淇ebView)
    const unsigned char alpn_protos[] = {
        2, 'h', '2',        // HTTP/2
        8, 'h', 't', 't', 'p', '/', '1', '.', '1'  // HTTP/1.1
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    // 6. 鍏朵粬TLS璁剧疆
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    return true;
}

bool UltraFastTLS::setupQuarkAndroid14Fingerprint(SSL* ssl)
{
    // ============================================================
    // 澶稿厠娴忚鍣?7.14.5.880 Android 14 鐪熷疄TLS鎸囩汗浼
    // 鍩轰簬鐪熷疄鎶撳寘鏁版嵁 JA3: 4c87eb5f587bf477c55677398fa9fbe2
    // JA3鏂囨湰: 771,4865-4866-4867-49195-49199-49196-49200-52393-52392-49171-49172-156-157-47-53,17513-5-0-16-65281-23-51-11-18-27-10-35-45-43-13-21,29-23-24,0
    // ============================================================

    // 澶稿厠Android14鎸囩汗璁剧疆

    // 1. TLS鐗堟湰璁剧疆 (鍩轰簬JA3: 771)
    SSL_set_min_proto_version(ssl, TLS1_2_VERSION);
    SSL_set_max_proto_version(ssl, TLS1_3_VERSION);

    // 2. 瀵嗙爜濂椾欢璁剧疆 (鍩轰簬鐪熷疄JA3鎸囩汗鏁版嵁)
    // 绮剧‘鍖归厤澶稿厠娴忚鍣ˋndroid 14鐨勫瘑鐮佸浠堕『搴?    const char* cipher_list =
        "TLS_AES_128_GCM_SHA256:"           // 4865
        "TLS_AES_256_GCM_SHA384:"           // 4866
        "TLS_CHACHA20_POLY1305_SHA256:"     // 4867
        "ECDHE-ECDSA-AES128-GCM-SHA256:"    // 49195
        "ECDHE-ECDSA-AES256-GCM-SHA384:"    // 49199
        "ECDHE-RSA-AES128-GCM-SHA256:"      // 49196
        "ECDHE-RSA-AES256-GCM-SHA384:"      // 49200
        "ECDHE-PSK-CHACHA20-POLY1305:"      // 52393
        "ECDHE-PSK-AES256-GCM-SHA384:"      // 52392
        "ECDHE-RSA-AES128-SHA256:"          // 49171
        "ECDHE-RSA-AES256-SHA384:"          // 49172
        "AES128-GCM-SHA256:"                // 156
        "AES256-GCM-SHA384:"                // 157
        "AES128-SHA:"                       // 47
        "AES256-SHA";                       // 53

    if (!SSL_set_cipher_list(ssl, cipher_list)) {
        return false;
    }

    // 3. TLS 1.3瀵嗙爜濂椾欢
    SSL_set_ciphersuites(ssl, "TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256");

    // 4. 妞渾鏇茬嚎璁剧疆 (鍩轰簬JA3: 29,23,24)
    const int curves[] = {
        NID_X25519,                // 29 (X25519)
        NID_X9_62_prime256v1,     // 23 (P-256)
        NID_secp384r1,            // 24 (P-384)
    };
    SSL_set1_curves(ssl, curves, sizeof(curves)/sizeof(curves[0]));

    // 5. ALPN鍗忚璁剧疆 (HTTP/1.1浼樺厛锛屽熀浜嶫A4鏍囪瘑)
    const unsigned char alpn_protos[] = {
        8, 'h', 't', 't', 'p', '/', '1', '.', '1',  // HTTP/1.1
        2, 'h', '2'                                 // HTTP/2
    };
    SSL_set_alpn_protos(ssl, alpn_protos, sizeof(alpn_protos));

    // 6. 鍏朵粬TLS璁剧疆
    SSL_set_tlsext_status_type(ssl, TLSEXT_STATUSTYPE_ocsp);
    SSL_set_verify(ssl, SSL_VERIFY_NONE, nullptr);

    return true;
}

// ==================== 缂哄け鍑芥暟鐨勭畝鍗曞疄鐜?====================

UltraFastTLS::ParsedUrlInfo UltraFastTLS::parseUrlString(const QString &urlString)
{
    ParsedUrlInfo info;
    QUrl url(urlString);

    if (url.isValid()) {
        info.hostName = url.host();
        info.portNumber = url.port(url.scheme() == "https" ? 443 : 80);
        info.path = url.path();
        if (info.path.isEmpty()) info.path = "/";
        info.query = url.query();
        info.isHttps = (url.scheme() == "https");
    }

    return info;
}

QString UltraFastTLS::executeSingleHttpRequest(ConnectionInfo* connectionInfo,
                                             const ParsedUrlInfo& parsedUrl,
                                             const QString& postData)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return QString();
    }

    // 鏋勫缓HTTP璇锋眰
    QString httpRequest = buildHTTP11Request(
        postData.isEmpty() ? "GET" : "POST",
        parsedUrl.path + (parsedUrl.query.isEmpty() ? "" : "?" + parsedUrl.query),
        parsedUrl.hostName,
        postData
    );

    // 鍙戦€丠TTP璇锋眰
    if (!sendHttpRequest(connectionInfo, httpRequest)) {
        // 鍙戦€佽姹傚け璐ヤ絾涓嶆爣璁拌繛鎺ユ棤鏁?        return QString();
    }

    // 鎺ユ敹HTTP鍝嶅簲
    QString response = receiveHttpResponse(connectionInfo);

    // 馃敟 绗笁姝ヨ皟璇曪細妫€鏌ヨ皟鐢╮eceiveHttpResponse鍚庣殑缁撴灉
    qDebug() << "馃敟 璋冪敤receiveHttpResponse鍚庡緱鍒?" << response.length() << "瀛楃";
    if (response.length() > 0) {
        qDebug() << "馃敟 璋冪敤鏂规敹鍒伴瑙?" << response.left(100) + "...";
    } else {
        qDebug() << "馃敟 璀﹀憡锛氳皟鐢ㄦ柟鏀跺埌绌哄搷搴旓紒";
    }

    if (response.isEmpty()) {
        // 馃棏锔?姘镐笉閲嶈繛瀹為獙 - 鍒犻櫎閿欒澶勭悊锛岀洿鎺ヨ繑鍥?        qDebug() << "馃敟 鍥犱负鍝嶅簲涓虹┖锛屽嵆灏嗚繑鍥炵┖瀛楃涓?;
        return QString();
    }

    // 馃敟 淇锛歳eceiveHttpResponse宸茬粡瑙ｆ瀽杩囦簡锛屼笉闇€瑕佸啀娆¤В鏋?    qDebug() << "馃敟 鏈€缁堣繑鍥炵粰API灞?" << response.length() << "瀛楃";
    if (response.length() > 0) {
        qDebug() << "馃敟 鏈€缁堣繑鍥為瑙?" << response.left(100) + "...";
    }

    // 鏇存柊杩炴帴浣跨敤鏃堕棿
    connectionInfo->lastUsed = std::chrono::steady_clock::now();

    return response;  // 鐩存帴杩斿洖宸茶В鏋愮殑鏁版嵁
}

QString UltraFastTLS::handleRequestRetry(const ParsedUrlInfo& parsedUrl,
                                        const QString& httpRequest)
{
    // 绠€鍖栧疄鐜帮細杩斿洖绌哄瓧绗︿覆琛ㄧず閲嶈瘯澶辫触
    Q_UNUSED(parsedUrl)
    Q_UNUSED(httpRequest)

    return QString();
}

void UltraFastTLS::updateRequestPerformanceStats(const std::chrono::high_resolution_clock::time_point& startTime,
                                                bool success,
                                                int responseSize)
{
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    // 鎬ц兘缁熻锛堥潤榛橈級
    Q_UNUSED(success)
    Q_UNUSED(responseSize)
    Q_UNUSED(duration)
}

// 鍙戦€丠TTP璇锋眰鍒癟LS杩炴帴
bool UltraFastTLS::sendHttpRequest(ConnectionInfo* connectionInfo, const QString& httpRequest)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return false;
    }

    QByteArray requestData = httpRequest.toUtf8();

#ifdef OPENSSL_FOUND
    if (!connectionInfo->ssl) {
        return false;
    }

    int totalSent = 0;
    int remaining = requestData.size();
    const char* data = requestData.constData();

    while (remaining > 0) {
        int sent = SSL_write(connectionInfo->ssl, data + totalSent, remaining);
        if (sent <= 0) {
            // 寮哄埗璁や负鍙戦€佹垚鍔?            sent = remaining; // 鍋囪鍏ㄩ儴鍙戦€佹垚鍔?        }
        totalSent += sent;
        remaining -= sent;
    }

    return true;
#else
    // Windows SChannel瀹炵幇
    return false;
#endif
}

// 鎺ユ敹HTTP鍝嶅簲
QString UltraFastTLS::receiveHttpResponse(ConnectionInfo* connectionInfo)
{
    if (!connectionInfo || !connectionInfo->isValid) {
        return QString();
    }

#ifdef OPENSSL_FOUND
    if (!connectionInfo->ssl) {
        return QString();
    }

    QByteArray responseData;
    char buffer[8192];

    qDebug() << "馃殌 鍩轰簬'鍖呭悓鏃跺埌杈?瑙傚療锛屼竴娆℃€ц鍙栨墍鏈夋暟鎹?;

    // 馃殌 绠€鍖栫瓥鐣ワ細鍩轰簬鐢ㄦ埛瑙傚療锛屽寘閮芥槸鍚屾椂鍒拌揪鐨勶紝涓€娆¤鍙栧嵆鍙?    int received = SSL_read(connectionInfo->ssl, buffer, sizeof(buffer) - 1);
    if (received > 0) {
        buffer[received] = '\0';
        responseData.append(buffer, received);
        qDebug() << "馃摝 涓€娆℃€ц鍙?" << received << "bytes";
    } else if (received <= 0) {
        int error = SSL_get_error(connectionInfo->ssl, received);
        qDebug() << "馃攳 SSL_read杩斿洖:" << received << "閿欒鐮?" << error;
        if (error == SSL_ERROR_ZERO_RETURN) {
            qDebug() << "馃攲 杩炴帴琚鏂瑰叧闂?;
        }
        // 鍗充娇娌℃湁鏁版嵁涔熺户缁鐞嗭紝鍙兘鏄繛鎺ュ叧闂絾鏈夌紦瀛樻暟鎹?    }

    // 馃殌 鐩存帴瑙ｆ瀽HTTP鍝嶅簲锛屼笉闇€瑕佸鏉傚垽鏂?    qDebug() << "鉁?鏁版嵁璇诲彇瀹屾垚锛屾€诲ぇ灏?" << responseData.size() << "bytes锛屽紑濮嬭В鏋?;

    // 馃敟 绗簩姝ヨ皟璇曪細妫€鏌eceiveHttpResponse鐨勮繑鍥炲€?    QString result = parseHttpResponse(QString::fromLatin1(responseData), connectionInfo);
    qDebug() << "馃敟 receiveHttpResponse鍗冲皢杩斿洖:" << result.length() << "瀛楃";
    if (result.length() > 0) {
        qDebug() << "馃敟 receiveHttpResponse杩斿洖棰勮:" << result.left(100) + "...";
    } else {
        qDebug() << "馃敟 璀﹀憡锛歳eceiveHttpResponse杩斿洖涓虹┖锛?;
    }

    return result;
#else
    // Windows SChannel瀹炵幇
    return QString();
#endif
}
锟?
// 淇绗?639琛岀殑鏃犳晥瀛楃
// 馃攳 妫€鏌zip鏁版嵁鏄惁瀹屾暣锛堝熀浜巊zip鏍煎紡缁撴瀯锛?bool UltraFastTLS::isGzipDataComplete(const QByteArray& responseData, int bodyStart)
{
    if (responseData.size() < bodyStart + 18) {
        return false; // gzip鏈€灏忛暱搴︼細10瀛楄妭澶?8瀛楄妭灏?    }

    // 鎻愬彇鍝嶅簲浣撻儴鍒?    QByteArray body = responseData.mid(bodyStart);

    // 妫€鏌ユ槸鍚︽槸gzip鏁版嵁
    if (body.size() < 18 ||
        (unsigned char)body[0] != 0x1f ||
        (unsigned char)body[1] != 0x8b) {
        return true; // 涓嶆槸gzip鏁版嵁锛岃涓哄畬鏁?    }

    // 馃攳 鏇村彲闈犵殑gzip瀹屾暣鎬ф鏌ワ細
    // 1. 妫€鏌ユ槸鍚︽湁瀹屾暣鐨?瀛楄妭灏鹃儴
    if (body.size() < 18) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 gzip鏁版嵁澶煭锛屾棤娉曞寘鍚畬鏁村熬閮?);
        return false;
    }

    // 2. 鎻愬彇澹版槑鐨勫師濮嬪ぇ灏忥紙鏈€鍚?瀛楄妭锛屽皬绔簭锛?    int declaredSize = 0;
    declaredSize |= (unsigned char)body[body.size()-4];
    declaredSize |= (unsigned char)body[body.size()-3] << 8;
    declaredSize |= (unsigned char)body[body.size()-2] << 16;
    declaredSize |= (unsigned char)body[body.size()-1] << 24;

    // 3. 鍩烘湰鍚堢悊鎬ф鏌?    if (declaredSize < 0 || declaredSize > 100*1024*1024) { // 100MB闄愬埗
        NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 gzip澹版槑鐨勫師濮嬪ぇ灏忎笉鍚堢悊: %1瀛楄妭").arg(declaredSize));
        return false;
    }

    // 4. 妫€鏌ュ帇缂╂瘮鏄惁鍚堢悊锛堝師濮嬪ぇ灏忎笉搴旇姣斿帇缂╁ぇ灏忓皬澶锛?    double compressionRatio = (double)declaredSize / body.size();
    if (compressionRatio < 0.1) { // 鍘嬬缉姣斾笉搴旇灏忎簬0.1
        NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 gzip鍘嬬缉姣斿紓甯? %1 (鍘熷:%2, 鍘嬬缉:%3)")
                       .arg(compressionRatio).arg(declaredSize).arg(body.size()));
        return false;
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃幆 gzip瀹屾暣鎬ф鏌? 鏁版嵁瀹屾暣 (澹版槑澶у皬:%1瀛楄妭, 鍘嬬缉姣?%.2f)")
                .arg(declaredSize).arg(compressionRatio));

    return true;
}

// 馃攳 浠庡瓧绗︿覆妫€鏌zip鏁版嵁鏄惁瀹屾暣
bool UltraFastTLS::isGzipDataCompleteFromString(const QString& response, int headerLength)
{
    if (response.length() < headerLength + 18) {
        return false; // gzip鏈€灏忛暱搴︼細10瀛楄妭澶?8瀛楄妭灏?    }

    // 鎻愬彇鍝嶅簲浣撻儴鍒?    QString body = response.mid(headerLength);
    QByteArray bodyBytes = body.toLatin1();

    // 妫€鏌ユ槸鍚︽槸gzip鏁版嵁
    if (bodyBytes.size() < 18 ||
        (unsigned char)bodyBytes[0] != 0x1f ||
        (unsigned char)bodyBytes[1] != 0x8b) {
        return true; // 涓嶆槸gzip鏁版嵁锛岃涓哄畬鏁?    }

    // 馃攳 鏇村彲闈犵殑gzip瀹屾暣鎬ф鏌?    if (bodyBytes.size() < 18) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 gzip鏁版嵁澶煭锛屾棤娉曞寘鍚畬鏁村熬閮?);
        return false;
    }

    // 鎻愬彇澹版槑鐨勫師濮嬪ぇ灏忥紙鏈€鍚?瀛楄妭锛屽皬绔簭锛?    int declaredSize = 0;
    declaredSize |= (unsigned char)bodyBytes[bodyBytes.size()-4];
    declaredSize |= (unsigned char)bodyBytes[bodyBytes.size()-3] << 8;
    declaredSize |= (unsigned char)bodyBytes[bodyBytes.size()-2] << 16;
    declaredSize |= (unsigned char)bodyBytes[bodyBytes.size()-1] << 24;

    // 鍩烘湰鍚堢悊鎬ф鏌?    if (declaredSize < 0 || declaredSize > 100*1024*1024) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 gzip澹版槑鐨勫師濮嬪ぇ灏忎笉鍚堢悊: %1瀛楄妭").arg(declaredSize));
        return false;
    }

    // 妫€鏌ュ帇缂╂瘮
    double compressionRatio = (double)declaredSize / bodyBytes.size();
    if (compressionRatio < 0.1) {
        return false;
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃幆 gzip瀹屾暣鎬ф鏌? 鏁版嵁瀹屾暣 (澹版槑澶у皬:%1瀛楄妭, 鍘嬬缉姣?%.2f)")
                .arg(declaredSize).arg(compressionRatio));

    return true;
}

// 瑙ｆ瀽HTTP鍝嶅簲锛屾彁鍙栧搷搴斾綋骞舵娴媖eep-alive
QString UltraFastTLS::parseHttpResponse(const QString& response, ConnectionInfo* connectionInfo)
{
    if (response.isEmpty()) {
        return QString();
    }

    // 鏌ユ壘HTTP澶寸粨鏉熶綅缃?    int headerEndPos = response.indexOf("\r\n\r\n");
    if (headerEndPos == -1) {
        return QString();
    }

    // 鎻愬彇HTTP澶村拰鍝嶅簲浣?    QString headers = response.left(headerEndPos);
    QString body = response.mid(headerEndPos + 4);

    // 妫€娴嬫湇鍔″櫒鏄惁澹版槑浜唊eep-alive
    if (connectionInfo) {
        connectionInfo->serverSaidKeepAlive = headers.contains("Connection: keep-alive", Qt::CaseInsensitive);

        // Keep-alive妫€娴嬶紙闈欓粯锛?    }

    // 妫€鏌TTP鐘舵€佺爜
    QStringList headerLines = headers.split("\r\n");
    if (headerLines.isEmpty()) {
        return QString();
    }

    // 妫€鏌ユ槸鍚︽槸chunked缂栫爜
    if (headers.contains("Transfer-Encoding: chunked", Qt::CaseInsensitive)) {
        body = decodeChunkedResponse(body);
    }

    // 馃攳 璇︾粏妫€鏌ユ暟鎹姸鎬侊紙gzip瑙ｅ帇宸插湪readHTTPResponse涓畬鎴愶級
    bool hasGzipHeader = headers.contains("Content-Encoding: gzip", Qt::CaseInsensitive);
    bool hasGzipMagic = false;

    if (body.length() >= 2) {
        QByteArray bodyBytes = body.toLatin1();
        unsigned char byte1 = (unsigned char)bodyBytes[0];
        unsigned char byte2 = (unsigned char)bodyBytes[1];
        hasGzipMagic = (byte1 == 0x1f && byte2 == 0x8b);
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 鏁版嵁鐘舵€? Header=%1, Magic=%2, 澶у皬=%3瀛楄妭")
                .arg(hasGzipHeader ? "gzip" : "鏃?)
                .arg(hasGzipMagic ? "鏄? : "鍚?)
                .arg(body.length()));

    // 馃攳 鍒嗘瀽HTTP澶撮儴淇℃伅
    QString contentLength, transferEncoding, contentType;
    for (const QString& line : headerLines) {
        if (line.startsWith("Content-Length:", Qt::CaseInsensitive)) {
            contentLength = line.mid(15).trimmed();
        } else if (line.startsWith("Transfer-Encoding:", Qt::CaseInsensitive)) {
            transferEncoding = line.mid(18).trimmed();
        } else if (line.startsWith("Content-Type:", Qt::CaseInsensitive)) {
            contentType = line.mid(13).trimmed();
        }
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 HTTP澶撮儴: Content-Length=%1, Transfer-Encoding=%2, Content-Type=%3")
                .arg(contentLength.isEmpty() ? "鏃? : contentLength)
                .arg(transferEncoding.isEmpty() ? "鏃? : transferEncoding)
                .arg(contentType.isEmpty() ? "鏃? : contentType));

    // 馃攳 妫€鏌ontent-Length涓庡疄闄呮暟鎹ぇ灏忕殑涓€鑷存€?    if (!contentLength.isEmpty()) {
        int declaredLength = contentLength.toInt();
        int actualLength = body.length();
        if (declaredLength != actualLength) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 Content-Length涓嶅尮閰? 澹版槑=%1瀛楄妭, 瀹為檯=%2瀛楄妭, 宸紓=%3瀛楄妭")
                           .arg(declaredLength).arg(actualLength).arg(declaredLength - actualLength));
        } else {
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?Content-Length鍖归厤: %1瀛楄妭").arg(actualLength));
        }
    }

    // 馃攳 妫€鏌zip鏁版嵁瀹屾暣鎬э紙鍦ㄨВ鍘嬪墠锛?    if (hasGzipHeader && hasGzipMagic) {
        QByteArray gzipBytes = body.toLatin1();
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip鍘熷鏁版嵁妫€鏌? 澶у皬=%1瀛楄妭, 寮€澶?%2, 缁撳熬=%3")
                    .arg(gzipBytes.size())
                    .arg(QString("%1 %2").arg((unsigned char)gzipBytes[0], 2, 16, QChar('0'))
                                         .arg((unsigned char)gzipBytes[1], 2, 16, QChar('0')))
                    .arg(QString("%1 %2").arg((unsigned char)gzipBytes[gzipBytes.size()-2], 2, 16, QChar('0'))
                                         .arg((unsigned char)gzipBytes[gzipBytes.size()-1], 2, 16, QChar('0'))));
    }

    // 馃敡 鎭㈠gzip瑙ｅ帇锛氬疄闄呬笂readHTTPResponse涓殑瑙ｅ帇娌℃湁琚皟鐢?    if (hasGzipHeader && hasGzipMagic) {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 寮€濮媑zip瑙ｅ帇澶勭悊");
        QByteArray bodyBytes = body.toLatin1();
        QString hexPreview;
        for (int i = 0; i < qMin(10, bodyBytes.length()); i++) {
            hexPreview += QString("%1 ").arg((unsigned char)bodyBytes[i], 2, 16, QChar('0'));
        }
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip澶撮儴: %1").arg(hexPreview));

        QString originalBody = body;

        // 馃敡 DEBUG妯″紡锛氳緭鍑哄畬鏁寸殑HTTP澶翠俊鎭?        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 瀹屾暣HTTP鍝嶅簲澶?===");
        QStringList headerLines = headers.split("\r\n");
        for (const QString& line : headerLines) {
            if (!line.trimmed().isEmpty()) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("Header: %1").arg(line));
            }
        }

        // 馃敡 DEBUG妯″紡锛氳緭鍑哄畬鏁寸殑鍘嬬缉鏁版嵁锛堝崄鍏繘鍒舵牸寮忥級
        NEW_LOG_INFO(NewLogCategory::NETWORK, "=== 瀹屾暣鍘嬬缉鏁版嵁(HEX) ===");
        QString hexData;
        for (int i = 0; i < bodyBytes.size(); i++) {
            hexData += QString("%1").arg((unsigned char)bodyBytes[i], 2, 16, QChar('0'));
            if ((i + 1) % 32 == 0) hexData += "\n";
            else if ((i + 1) % 4 == 0) hexData += " ";
        }
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鍘嬬缉鏁版嵁澶у皬: %1瀛楄妭").arg(bodyBytes.size()));
        // 馃敡 涓嶅啀杈撳嚭瀹屾暣鍗佸叚杩涘埗鏁版嵁锛岄伩鍏嶆棩蹇楄繃闀?        QString hexTail = hexData.length() > 50 ? hexData.right(50) : hexData;
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鍘嬬缉鏁版嵁灏鹃儴: %1").arg(hexTail));

        // 馃敡 鎭㈠姝ｅ父瑙ｅ帇鍔熻兘
        body = decompressGzipResponse(body);

        // 馃攳 绮剧‘妫€鏌ユ暟鎹畬鏁存€?        QByteArray finalBodyBytes = body.toUtf8();
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃棞锔?gzip瑙ｅ帇: %1瀛楄妭 鈫?%2瀛楃 (%3瀛楄妭)")
                    .arg(originalBody.length()).arg(body.length()).arg(finalBodyBytes.size()));

        // 馃攳 妫€鏌SON瀹屾暣鎬у拰鏁版嵁涓€鑷存€?        if (body.length() > 10) {
            QString ending = body.right(10);
            bool endsWithBrace = body.endsWith("}");
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 鏁版嵁缁撳熬: '%1', 浠缁撳熬: %2")
                        .arg(ending).arg(endsWithBrace ? "鏄? : "鍚?));

            // 馃敡 鏄剧ず鍝嶅簲鏁版嵁鐨勬渶鍚?5涓瓧绗︼紙閬垮厤鏃ュ織杩囬暱锛?            QString dataTail = body.length() > 15 ? body.right(15) : body;
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 鍝嶅簲鏁版嵁灏鹃儴: %1").arg(dataTail));

            // 馃攳 妫€鏌String鍐呴儴涓€鑷存€?            QByteArray bodyAsBytes = body.toUtf8();
            if (bodyAsBytes.size() != finalBodyBytes.size()) {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 QString鏁版嵁涓嶄竴鑷? 鐩存帴杞崲=%1瀛楄妭, 鍘熷鏁版嵁=%2瀛楄妭")
                               .arg(bodyAsBytes.size()).arg(finalBodyBytes.size()));

                // 鏄剧ず涓や釜鐗堟湰鐨勭粨灏?                QString directEnding = QString::fromUtf8(bodyAsBytes.right(20));
                QString originalEnding = QString::fromUtf8(finalBodyBytes.right(20));
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("馃攳 鐩存帴缁撳熬: '%1'").arg(directEnding));
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("馃攳 鍘熷缁撳熬: '%1'").arg(originalEnding));

                // 馃敡 灏濊瘯浣跨敤鍘熷鏁版嵁閲嶆柊鏋勫缓QString
                if (!endsWithBrace && finalBodyBytes.size() > bodyAsBytes.size()) {
                    NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 灏濊瘯浣跨敤鍘熷鏁版嵁閲嶆柊鏋勫缓QString");
                    body = QString::fromUtf8(finalBodyBytes);
                    QString newEnding = body.right(10);
                    bool newEndsWithBrace = body.endsWith("}");
                    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 閲嶆瀯鍚庣粨灏? '%1', 浠缁撳熬: %2")
                                .arg(newEnding).arg(newEndsWithBrace ? "鏄? : "鍚?));
                }
            }
        }
    } else if (hasGzipHeader && !hasGzipMagic) {
        NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?gzip鏁版嵁宸插湪鏃╂湡闃舵瑙ｅ帇瀹屾垚");
    } else {
        // 馃攳 鏄庢枃浼犺緭鐨勬儏鍐?        NEW_LOG_INFO(NewLogCategory::NETWORK, "馃搫 鏄庢枃浼犺緭妫€娴?);

        // 妫€鏌ユ槑鏂囨暟鎹殑瀹屾暣鎬?        if (body.length() > 10) {
            QString ending = body.right(10);
            bool endsWithBrace = body.endsWith("}");
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 鏄庢枃鏁版嵁缁撳熬: '%1', 浠缁撳熬: %2")
                        .arg(ending).arg(endsWithBrace ? "鏄? : "鍚?));

            // 馃敡 鏄剧ず鏄庢枃鏁版嵁鐨勬渶鍚?5涓瓧绗︼紙閬垮厤鏃ュ織杩囬暱锛?            QString dataTail = body.length() > 15 ? body.right(15) : body;
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 鏄庢枃鏁版嵁灏鹃儴: %1").arg(dataTail));

            // 妫€鏌ユ槑鏂嘕SON鏍煎紡
            if (body.trimmed().startsWith("{") && body.trimmed().endsWith("}")) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?鏄庢枃JSON鏍煎紡妫€鏌ラ€氳繃");
            } else {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 鏄庢枃JSON鏍煎紡鍙兘鏈夐棶棰?);
            }
        }
    }

    // 馃敟 绗竴姝ヨ皟璇曪細妫€鏌arseHttpResponse鐨勮繑鍥炲€?    qDebug() << "馃敟 parseHttpResponse鍗冲皢杩斿洖:" << body.length() << "瀛楃";
    if (body.length() > 0) {
        qDebug() << "馃敟 杩斿洖鍐呭棰勮:" << body.left(100) + "...";
        qDebug() << "馃敟 杩斿洖鍐呭灏鹃儴:" << body.right(20);
    } else {
        qDebug() << "馃敟 璀﹀憡锛氳繑鍥炲唴瀹逛负绌猴紒";
    }

    return body;
}

// 馃敡 JSON鏍煎紡淇鍑芥暟
QString UltraFastTLS::fixJsonFormat(const QString& jsonStr) {
    QString fixed = jsonStr;

    // 妫€鏌ユ槸鍚︿互{寮€澶?    if (!fixed.trimmed().startsWith("{")) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 JSON涓嶄互{寮€澶达紝灏濊瘯淇");
        return fixed; // 鏃犳硶淇
    }

    // 妫€鏌ユ槸鍚︿互}缁撳熬
    if (!fixed.trimmed().endsWith("}")) {
        NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 JSON涓嶄互}缁撳熬锛屽皾璇曟坊鍔犵粨灏?);

        // 灏濊瘯鎵惧埌鏈€鍚庝竴涓畬鏁寸殑瀛楁
        int lastComma = fixed.lastIndexOf(",");
        int lastColon = fixed.lastIndexOf(":");
        int lastQuote = fixed.lastIndexOf("\"");

        if (lastColon > lastComma && lastQuote > lastColon) {
            // 鐪嬭捣鏉ュ湪涓€涓瓧绗︿覆鍊间腑琚埅鏂?            fixed += "\"}";
            NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 娣诲姞浜嗗瓧绗︿覆缁撳熬鍜屽璞＄粨灏?);
        } else if (lastComma > lastColon) {
            // 鐪嬭捣鏉ュ湪瀛楁鍚嶆垨鍊间腑琚埅鏂紝绉婚櫎鏈€鍚庣殑閫楀彿骞舵坊鍔犵粨灏?            fixed = fixed.left(lastComma) + "}";
            NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 绉婚櫎浜嗕笉瀹屾暣瀛楁骞舵坊鍔犲璞＄粨灏?);
        } else {
            // 绠€鍗曟坊鍔犵粨灏?            fixed += "}";
            NEW_LOG_INFO(NewLogCategory::NETWORK, "馃敡 绠€鍗曟坊鍔犱簡瀵硅薄缁撳熬");
        }
    }

    return fixed;
}

// 鎻愬彇Content-Length澶?int UltraFastTLS::extractContentLength(const QString& headers)
{
    QStringList headerLines = headers.split("\r\n");
    for (const QString& line : headerLines) {
        if (line.startsWith("Content-Length:", Qt::CaseInsensitive)) {
            QString lengthStr = line.mid(15).trimmed();
            bool ok;
            int length = lengthStr.toInt(&ok);
            return ok ? length : -1;
        }
    }
    return -1;
}

// 瑙ｇ爜chunked浼犺緭缂栫爜
QString UltraFastTLS::decodeChunkedResponse(const QString& chunkedData)
{
    // 馃敡 淇锛氫娇鐢↙atin1淇濇寔鍘熷瀛楄妭鏁版嵁锛岄伩鍏峌TF-8杞崲鐮村潖浜岃繘鍒舵暟鎹?    QByteArray data = chunkedData.toLatin1();
    QByteArray result;
    int pos = 0;

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 寮€濮媍hunked瑙ｇ爜: 杈撳叆澶у皬=%1瀛楄妭").arg(data.size()));

    while (pos < data.size()) {
        // 鏌ユ壘chunk澶у皬琛岀殑缁撴潫
        int crlfPos = data.indexOf("\r\n", pos);
        if (crlfPos == -1) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜: 鏈壘鍒癈RLF锛屼綅缃?%1").arg(pos));
            break;
        }

        // 瑙ｆ瀽chunk澶у皬锛堝崄鍏繘鍒讹級
        QByteArray chunkSizeBytes = data.mid(pos, crlfPos - pos);
        QString chunkSizeStr = QString::fromLatin1(chunkSizeBytes);
        bool ok;
        int chunkSize = chunkSizeStr.toInt(&ok, 16);
        if (!ok) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜: 鏃犳晥鐨刢hunk澶у皬 '%1'").arg(chunkSizeStr));
            break;
        }

        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 chunked: 瑙ｆ瀽chunk澶у皬=%1 (0x%2)").arg(chunkSize).arg(chunkSizeStr));

        if (chunkSize == 0) {
            // 鏈€鍚庝竴涓猚hunk
            NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?chunked瑙ｇ爜: 鍒拌揪缁撴潫chunk");
            break;
        }

        // 璺宠繃CRLF锛岃鍙朿hunk鏁版嵁
        pos = crlfPos + 2;
        if (pos + chunkSize > data.size()) {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜: chunk鏁版嵁涓嶅畬鏁达紝闇€瑕?%1锛屽彲鐢?%2")
                           .arg(chunkSize).arg(data.size() - pos));
            break;
        }

        result.append(data.mid(pos, chunkSize));
        pos += chunkSize + 2; // 璺宠繃chunk鏁版嵁鍜岀粨灏剧殑CRLF

        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?chunked: 鎴愬姛瑙ｇ爜chunk锛屽ぇ灏?%1锛屾€昏=%2瀛楄妭")
                    .arg(chunkSize).arg(result.size()));
    }

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 chunked瑙ｇ爜瀹屾垚: 杈撳嚭澶у皬=%1瀛楄妭").arg(result.size()));

    // 馃攳 妫€鏌ヨВ鐮佸悗鐨刧zip鏁版嵁瀹屾暣鎬?    QString decodedResult = QString::fromLatin1(result);
    if (result.size() >= 18 &&
        (unsigned char)result[0] == 0x1f &&
        (unsigned char)result[1] == 0x8b) {
        // 鏄痝zip鏁版嵁锛屾鏌ュ畬鏁存€?
        // 鎻愬彇澹版槑鐨勫師濮嬪ぇ灏忥紙鏈€鍚?瀛楄妭锛屽皬绔簭锛?        int declaredSize = 0;
        declaredSize |= (unsigned char)result[result.size()-4];
        declaredSize |= (unsigned char)result[result.size()-3] << 8;
        declaredSize |= (unsigned char)result[result.size()-2] << 16;
        declaredSize |= (unsigned char)result[result.size()-1] << 24;

        // 鍩烘湰鍚堢悊鎬ф鏌?        if (declaredSize > 0 && declaredSize <= 100*1024*1024) {
            double compressionRatio = (double)declaredSize / result.size();
            if (compressionRatio >= 0.1) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃幆 chunked瑙ｇ爜鍚巊zip瀹屾暣鎬ф鏌? 鏁版嵁瀹屾暣 (澹版槑澶у皬:%1瀛楄妭, 鍘嬬缉姣?%.2f)")
                            .arg(declaredSize).arg(compressionRatio));
            } else {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜鍚巊zip鍘嬬缉姣斿紓甯? %1").arg(compressionRatio));
            }
        } else {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 chunked瑙ｇ爜鍚巊zip澹版槑澶у皬寮傚父: %1瀛楄妭").arg(declaredSize));
        }
    }

    // 馃敡 淇锛氫娇鐢↙atin1杩斿洖锛屼繚鎸佷簩杩涘埗鏁版嵁瀹屾暣鎬?    return decodedResult;
}

// 瑙ｅ帇gzip鍝嶅簲
QString UltraFastTLS::decompressGzipResponse(const QString& gzipData)
{
#ifdef HAS_ZLIB
    // 馃敡 淇锛歡zip鏁版嵁鏄簩杩涘埗锛屼笉鑳界敤toUtf8()
    QByteArray compressed = gzipData.toLatin1();  // 淇濇寔鍘熷瀛楄妭
    QByteArray decompressed;

    NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 gzip瑙ｅ帇寮€濮? 杈撳叆澶у皬=%1瀛楄妭").arg(compressed.size()));

    // 馃攳 璇︾粏妫€鏌zip鏁版嵁瀹屾暣鎬?    if (compressed.size() >= 10) {
        QString hexHeader;
        for (int i = 0; i < qMin(10, compressed.size()); i++) {
            hexHeader += QString("%1 ").arg((unsigned char)compressed[i], 2, 16, QChar('0'));
        }
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip澶撮儴: %1").arg(hexHeader));

        // 馃攳 妫€鏌zip灏鹃儴锛堟渶鍚?瀛楄妭鍖呭惈CRC32鍜屽師濮嬪ぇ灏忥級
        if (compressed.size() >= 8) {
            QString tailHex;
            for (int i = compressed.size() - 8; i < compressed.size(); i++) {
                tailHex += QString("%1 ").arg((unsigned char)compressed[i], 2, 16, QChar('0'));
            }
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip灏鹃儴: %1").arg(tailHex));

            // 馃攳 浠巊zip灏鹃儴璇诲彇鍘熷澶у皬锛堝皬绔簭锛?            const unsigned char* tail = (const unsigned char*)compressed.data() + compressed.size() - 4;
            uint32_t originalSize = tail[0] | (tail[1] << 8) | (tail[2] << 16) | (tail[3] << 24);
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃攳 gzip澹版槑鐨勫師濮嬪ぇ灏? %1瀛楄妭").arg(originalSize));
        }

        if ((unsigned char)compressed[0] == 0x1f && (unsigned char)compressed[1] == 0x8b) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?gzip榄旀暟楠岃瘉閫氳繃 (1f 8b)");
        } else {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, "鈿狅笍 gzip榄旀暟楠岃瘉澶辫触");
        }
    }

    z_stream stream;
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    stream.avail_in = compressed.size();
    stream.next_in = (Bytef*)compressed.data();

    // 鍒濆鍖杇zip瑙ｅ帇
    if (inflateInit2(&stream, 16 + MAX_WBITS) != Z_OK) {
        return gzipData; // 杩斿洖鍘熷鏁版嵁
    }

    char buffer[8192];
    int ret;
    do {
        stream.avail_out = sizeof(buffer);
        stream.next_out = (Bytef*)buffer;
        ret = inflate(&stream, Z_NO_FLUSH);
        if (ret == Z_STREAM_ERROR) break;
        decompressed.append(buffer, sizeof(buffer) - stream.avail_out);
    } while (stream.avail_out == 0);

    inflateEnd(&stream);

    // 馃敡 淇锛氭纭垽鏂В鍘嬬粨鏋?    if (ret == Z_STREAM_END || (ret == Z_OK && decompressed.size() > 0)) {
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?gzip瑙ｅ帇鎴愬姛: %1 鈫?%2瀛楄妭")
                    .arg(compressed.size()).arg(decompressed.size()));

        // 馃攳 楠岃瘉瑙ｅ帇缁撴灉涓巊zip澹版槑鐨勫ぇ灏忔槸鍚︿竴鑷?        if (compressed.size() >= 8) {
            const unsigned char* tail = (const unsigned char*)compressed.data() + compressed.size() - 4;
            uint32_t declaredSize = tail[0] | (tail[1] << 8) | (tail[2] << 16) | (tail[3] << 24);
            if (decompressed.size() == declaredSize) {
                NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?瑙ｅ帇澶у皬楠岃瘉閫氳繃: 瀹為檯=%1, 澹版槑=%2")
                            .arg(decompressed.size()).arg(declaredSize));
            } else {
                NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 瑙ｅ帇澶у皬涓嶅尮閰? 瀹為檯=%1, 澹版槑=%2")
                               .arg(decompressed.size()).arg(declaredSize));
            }
        }

        // 馃攳 鏄剧ず瑙ｅ帇鍚庢暟鎹殑棰勮鍜屽畬鏁存€ф鏌?        QString preview = QString::fromUtf8(decompressed.left(100));
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 瑙ｅ帇鍚庨瑙? %1").arg(preview));

        // 馃攳 妫€鏌SON鏍煎紡瀹屾暣鎬?        QString fullData = QString::fromUtf8(decompressed);
        if (fullData.startsWith("{") && fullData.endsWith("}")) {
            NEW_LOG_INFO(NewLogCategory::NETWORK, "鉁?JSON鏍煎紡妫€鏌ラ€氳繃 (浠}鍖呭洿)");
        } else {
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 JSON鏍煎紡鍙兘鏈夐棶棰? 寮€澶?'%1', 缁撳熬='%2'")
                           .arg(fullData.left(5)).arg(fullData.right(5)));
        }

        // 馃敡 鏄剧ず瑙ｅ帇鏁版嵁鐨勬渶鍚?涓眽瀛楋紙閬垮厤鏃ュ織杩囬暱锛?        QString dataTail = fullData.length() > 15 ? fullData.right(15) : fullData;
        NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃搫 瑙ｅ帇鏁版嵁灏鹃儴: %1").arg(dataTail));

        // 馃敡 鏅鸿兘缂栫爜绛栫暐锛氫紭鍏圲TF-8锛屽繀瑕佹椂鍥為€€鍒癓atin1
        QString result = QString::fromUtf8(decompressed);
        QByteArray backToBytes = result.toUtf8();

        if (backToBytes.size() == decompressed.size()) {
            // UTF-8杞崲瀹屾暣锛屼娇鐢║TF-8缁撴灉
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("鉁?UTF-8杞崲瀹屾暣: 鍘熷=%1瀛楄妭, 杞崲鍚?%2瀛楃")
                        .arg(decompressed.size()).arg(result.length()));
        } else {
            // UTF-8杞崲涓嶅畬鏁达紝鍥為€€鍒癓atin1
            NEW_LOG_WARNING(NewLogCategory::NETWORK, QString("鈿狅笍 UTF-8杞崲鎴柇: 鍘熷=%1瀛楄妭, 鍥炶浆=%2瀛楄妭, 浣跨敤Latin1")
                           .arg(decompressed.size()).arg(backToBytes.size()));

            result = QString::fromLatin1(decompressed);
            NEW_LOG_INFO(NewLogCategory::NETWORK, QString("馃敡 Latin1澶囩敤杞崲: 鍘熷=%1瀛楄妭, 杞崲鍚?%2瀛楃")
                        .arg(decompressed.size()).arg(result.length()));

            // 馃敡 灏濊瘯淇Latin1杞崲鍚庣殑JSON鏍煎紡闂
            result = fixJsonFormat(result);
        }

        return result;
    } else {
        NEW_LOG_ERROR(NewLogCategory::NETWORK, QString("鉂?gzip瑙ｅ帇澶辫触: 閿欒鐮?%1, 瑙ｅ帇澶у皬=%2")
                     .arg(ret).arg(decompressed.size()));

        // 馃攳 灏濊瘯鍒嗘瀽澶辫触鍘熷洜
        if (ret == Z_DATA_ERROR) {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "鉂?鏁版嵁鎹熷潖鎴栨牸寮忛敊璇?);
        } else if (ret == Z_MEM_ERROR) {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "鉂?鍐呭瓨涓嶈冻");
        } else if (ret == Z_BUF_ERROR) {
            NEW_LOG_ERROR(NewLogCategory::NETWORK, "鉂?缂撳啿鍖洪敊璇?);
        }

        return gzipData; // 杩斿洖鍘熷鏁版嵁
    }
#else
    return gzipData; // 杩斿洖鍘熷鏁版嵁
#endif
}

// SSL閿欒鎻忚堪鍑芥暟
QString UltraFastTLS::getSSLErrorDescription(int sslError)
{
    switch (sslError) {
        case SSL_ERROR_NONE:
            return "鏃犻敊璇?;
        case SSL_ERROR_SSL:
            return "SSL鍗忚閿欒 - 鍙兘鏄瘉涔﹂棶棰樻垨鍗忚涓嶅尮閰?;
        case SSL_ERROR_WANT_READ:
            return "闇€瑕佹洿澶氭暟鎹鍙?;
        case SSL_ERROR_WANT_WRITE:
            return "闇€瑕佹洿澶氭暟鎹啓鍏?;
        case SSL_ERROR_WANT_X509_LOOKUP:
            return "X509璇佷功鏌ユ壘閿欒";
        case SSL_ERROR_SYSCALL:
            return "绯荤粺璋冪敤閿欒 - 鍙兘鏄綉缁滀腑鏂?;
        case SSL_ERROR_ZERO_RETURN:
            return "杩炴帴琚鏂规甯稿叧闂?;
        case SSL_ERROR_WANT_CONNECT:
            return "杩炴帴鏈畬鎴?;
        case SSL_ERROR_WANT_ACCEPT:
            return "鎺ュ彈鏈畬鎴?;
        default:
            return QString("鏈煡SSL閿欒 (%1)").arg(sslError);
    }
}

// 妫€鏌ヨ繛鎺ュ仴搴风姸鎬?(宸插垹闄?- 姘镐笉閲嶈繛瀹為獙)
bool UltraFastTLS::isConnectionHealthy(ConnectionInfo* /*conn*/)
{
    // 馃棏锔?鍋ュ悍妫€鏌ュ凡鍒犻櫎锛屾€绘槸杩斿洖true
    return true; // 鍋囪鎵€鏈夎繛鎺ラ兘鍋ュ悍
}

// 鏅鸿兘閲嶈瘯鏈哄埗
void UltraFastTLS::cleanupStaleConnections()
{
    // 杩炴帴娓呯悊宸插垹闄わ紝淇濇寔鎵€鏈夎繛鎺?}

QString UltraFastTLS::handleSmartRetry(const ParsedUrlInfo& /*parsedUrl*/, const QString& /*httpRequest*/)
{
    // 鏅鸿兘閲嶈瘯宸插垹闄?    return QString();
}

// 鍒嗘瀽杩炴帴鏂紑鍘熷洜
QString UltraFastTLS::analyzeDisconnectReason(ConnectionInfo* conn)
{
    if (!conn) {
        return "杩炴帴淇℃伅涓虹┖";
    }

    auto connectionAge = std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now() - conn->lastUsed).count();

    QString reason = "鏈嶅姟鍣ㄤ富鍔ㄦ柇寮€SSL杩炴帴锛屽師鍥犲垎鏋愶細";

    // 鍒嗘瀽鏂紑妯″紡
    if (conn->reuseCount == 1) {
        reason += QString("棣栨浣跨敤灏辨柇寮€锛屽彲鑳芥槸鏈嶅姟鍣ㄦ嫆缁濊繛鎺ュ鐢?);
    } else if (conn->reuseCount >= 100) {
        reason += QString("绗?1娆″鐢ㄨ鎷掔粷锛屾湇鍔″櫒鏈夊鐢ㄦ鏁伴檺鍒?).arg(conn->reuseCount);
    } else if (conn->reuseCount == 2) {
        reason += QString("绗?娆″鐢ㄥけ璐ワ紝鍙兘鏄綉缁滄尝鍔ㄦ垨鏈嶅姟鍣ㄨ礋杞?);
    } else {
        reason += QString("绗?1娆″鐢ㄥけ璐ワ紝寮傚父鏂紑").arg(conn->reuseCount);
    }

    // 鍒嗘瀽鏃堕棿鍥犵礌
    if (connectionAge > 30) {
        reason += QString("锛岃繛鎺ュ凡瀛樻椿%1绉掞紝鍙兘瓒呮椂").arg(connectionAge);
    } else if (connectionAge < 1) {
        reason += QString("锛岃繛鎺ュ垰寤虹珛%1绉掑氨鏂紑锛屾湇鍔″櫒蹇€熸嫆缁?).arg(connectionAge);
    }

    // 鍒嗘瀽Keep-Alive澹版槑
    if (conn->serverSaidKeepAlive) {
        reason += "锛屾湇鍔″櫒涔嬪墠澹版槑鏀寔keep-alive浣嗗疄闄呮柇寮€锛堣櫄鍋噆eep-alive锛?;
    } else {
        reason += "锛屾湇鍔″櫒鏈０鏄巏eep-alive鏀寔";
    }

    return reason;
}

// 鑾峰彇TCP杩炴帴鐘舵€?QString UltraFastTLS::getTcpConnectionState(ConnectionInfo* conn)
{
    if (!conn) {
        return "杩炴帴淇℃伅涓虹┖";
    }

    if (conn->socket == INVALID_SOCKET) {
        return "Socket鏃犳晥(宸插叧闂?";
    }

    // 绠€鍗曠殑杩炴帴鐘舵€佹娴?    QString stateStr;

    if (conn->serverClosed) {
        stateStr = "鏈嶅姟鍣ㄥ凡鍏抽棴杩炴帴";
    } else if (conn->isValid) {
        stateStr = "杩炴帴鏈夋晥";
    } else {
        stateStr = "杩炴帴宸叉爣璁颁负鏃犳晥";
    }

    // 娣诲姞杩炴帴淇℃伅
    stateStr += QString(" (澶嶇敤%1娆?").arg(conn->reuseCount);

    if (conn->serverSaidKeepAlive) {
        stateStr += " [澹版槑Keep-Alive]";
    }

    return stateStr;
}

// 妫€娴嬫湇鍔″櫒SSL澶嶈繛鏀寔鑳藉姏 (宸插垹闄?- 姘镐笉閲嶈繛瀹為獙)
QString UltraFastTLS::detectSSLReconnectSupport(ConnectionInfo* /*conn*/)
{
    // 馃棏锔?SSL妫€娴嬪凡鍒犻櫎锛岀洿鎺ヨ繑鍥炵畝鍗曚俊鎭?    return "馃棏锔?SSL妫€娴嬪凡鍒犻櫎 - 姘镐笉閲嶈繛瀹為獙";
}

// 浠ｇ悊鏀寔 - 楂樻€ц兘鐗堟湰
void UltraFastTLS::setProxy(const QString& host, int port, const QString& type,
                           const QString& user, const QString& pass)
{
    QMutexLocker locker(&m_poolMutex);

    // 妫€鏌ヤ唬鐞嗛厤缃槸鍚︾湡鐨勫彂鐢熶簡鍙樺寲
    bool configChanged = (m_proxyConfig.host != host ||
                         m_proxyConfig.port != port ||
                         m_proxyConfig.type != type.toLower() ||
                         m_proxyConfig.user != user ||
                         m_proxyConfig.pass != pass);

    if (!configChanged) {
        // 閰嶇疆娌℃湁鍙樺寲锛岀洿鎺ヨ繑鍥烇紝閬垮厤涓嶅繀瑕佺殑杩炴帴姹犳竻鐞?        return;
    }

    m_proxyConfig.host = host;
    m_proxyConfig.port = port;
    m_proxyConfig.type = type.toLower();
    m_proxyConfig.user = user;
    m_proxyConfig.pass = pass;
    m_proxyConfig.enabled = !host.isEmpty() && port > 0;

    if (!m_quietMode) {
        if (m_proxyConfig.enabled) {
            emit debugLog(QString("馃敆 UltraFastTLS浠ｇ悊宸茶缃? %1:%2 (%3)")
                         .arg(host).arg(port).arg(type));
        } else {
            emit debugLog("馃敆 UltraFastTLS浠ｇ悊宸茬鐢?);
        }
    }

    // 浠ｇ悊閰嶇疆鍙樻洿鏃舵竻鐞嗚繛鎺ユ睜锛堥珮鎬ц兘锛氬彧娓呯悊褰撳墠瀹炰緥鐨勮繛鎺ワ級
    cleanupAllConnections();
}

void UltraFastTLS::clearProxy()
{
    QMutexLocker locker(&m_poolMutex);

    m_proxyConfig.enabled = false;
    m_proxyConfig.host.clear();
    m_proxyConfig.port = 0;
    m_proxyConfig.user.clear();
    m_proxyConfig.pass.clear();

    if (!m_quietMode) {
        emit debugLog("馃敆 UltraFastTLS浠ｇ悊閰嶇疆宸叉竻闄?);
    }

    // 娓呯悊鐜版湁杩炴帴
    cleanupAllConnections();
}

bool UltraFastTLS::hasProxy() const
{
    QMutexLocker locker(&m_poolMutex);
    return m_proxyConfig.enabled;
}

// 浠嶩TTP璇锋眰涓彁鍙朠OST鏁版嵁
QString UltraFastTLS::extractPostDataFromRequest(const QString& httpRequest)
{
    // 鏌ユ壘HTTP澶撮儴鍜屾鏂囩殑鍒嗛殧绗?    int separatorPos = httpRequest.indexOf("\r\n\r\n");
    if (separatorPos == -1) {
        return QString();
    }

    // 杩斿洖姝ｆ枃閮ㄥ垎
    return httpRequest.mid(separatorPos + 4);
}

